# MyTribe API Documentation

This document contains all the API endpoints for the MyTribe application.

## Authentication APIs

### Sign Up
- **Endpoint**: `/api/auth/signup`
- **Method**: POST
- **Description**: Register a new user
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "<PERSON>",
    "phone": "+1234567890"
  }
  ```
- **Response**: 
  ```json
  {
    "success": true,
    "message": "User registered successfully",
    "userId": "123456"
  }
  ```

### Login
- **Endpoint**: `/api/auth/login`
- **Method**: POST
- **Description**: Authenticate a user
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**: 
  ```json
  {
    "success": true,
    "token": "jwt-token-here",
    "user": {
      "id": "123456",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "USER"
    }
  }
  ```

### Verify Mobile
- **Endpoint**: `/api/auth/verify-mobile`
- **Method**: POST
- **Description**: Verify user's mobile number with OTP
- **Request Body**:
  ```json
  {
    "phone": "+1234567890",
    "otp": "123456"
  }
  ```
- **Response**: 
  ```json
  {
    "success": true,
    "message": "Phone number verified successfully"
  }
  ```

## User Profile APIs

### Get User Profile
- **Endpoint**: `/api/users/profile`
- **Method**: GET
- **Description**: Get current user's profile
- **Headers**: Authorization: Bearer {token}
- **Response**: 
  ```json
  {
    "id": "123456",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "gender": "Male",
    "profession": "Software Engineer",
    "ethnicity": "Asian",
    "shortBio": "I love coding and meeting new people",
    "employmentStatus": "Full-time",
    "relationshipStatus": "Single",
    "zipCode": "12345",
    "availability": {
      "selectedTimeSlots": ["Thursday 7 PM", "Sunday 11 AM"]
    },
    "personalityType": "The Connector"
  }
  ```

### Update User Profile
- **Endpoint**: `/api/users/profile`
- **Method**: PUT
- **Description**: Update user profile information
- **Headers**: Authorization: Bearer {token}
- **Request Body**:
  ```json
  {
    "name": "John Doe",
    "gender": "Male",
    "profession": "Software Engineer",
    "ethnicity": "Asian",
    "shortBio": "I love coding and meeting new people",
    "employmentStatus": "Full-time",
    "relationshipStatus": "Single",
    "zipCode": "12345"
  }
  ```
- **Response**: 
  ```json
  {
    "success": true,
    "message": "Profile updated successfully"
  }
  ```

### Update Availability
- **Endpoint**: `/api/users/availability`
- **Method**: PUT
- **Description**: Update user availability
- **Headers**: Authorization: Bearer {token}
- **Request Body**:
  ```json
  {
    "selectedTimeSlots": ["Thursday 7 PM", "Friday 7 PM", "Sunday 11 AM"]
  }
  ```
- **Response**: 
  ```json
  {
    "success": true,
    "message": "Availability updated successfully"
  }
  ```

### Save Quiz Results
- **Endpoint**: `/api/users/personality`
- **Method**: POST
- **Description**: Save user's personality quiz results
- **Headers**: Authorization: Bearer {token}
- **Request Body**:
  ```json
  {
    "answers": {
      "1": "A",
      "2": "B",
      "3": "C",
      "4": "D",
      "5": "A"
    }
  }
  ```
- **Response**: 
  ```json
  {
    "success": true,
    "personalityType": "The Connector",
    "traits": ["Empathetic", "Social", "Supportive", "Inclusive"]
  }
  ```

## Group Matching APIs

### Get Matching Status
- **Endpoint**: `/api/match/status`
- **Method**: GET
- **Description**: Get user's current matching status
- **Headers**: Authorization: Bearer {token}
- **Response**: 
  ```json
  {
    "status": "PENDING",
    "message": "We're finding your perfect tribe match",
    "estimatedTimeRemaining": "24 hours"
  }
  ```

### Get Group
- **Endpoint**: `/api/groups/my-group`
- **Method**: GET
- **Description**: Get user's current group information
- **Headers**: Authorization: Bearer {token}
- **Response**: 
  ```json
  {
    "groupId": "G12345",
    "name": "Awesome Tribe",
    "members": [
      {
        "id": "123456",
        "name": "John Doe",
        "personalityType": "The Connector"
      },
      {
        "id": "789012",
        "name": "Jane Smith",
        "personalityType": "The Adventurer"
      }
    ],
    "nextMeetup": {
      "date": "2023-06-15T19:00:00Z",
      "location": "Central Park Coffee Shop"
    }
  }
  ```

## Admin APIs

### Get All Users
- **Endpoint**: `/api/admin/users`
- **Method**: GET
- **Description**: Get all users (admin only)
- **Headers**: Authorization: Bearer {token}
- **Response**: 
  ```json
  {
    "users": [
      {
        "id": "123456",
        "name": "John Doe",
        "email": "<EMAIL>",
        "status": "ACTIVE"
      },
      {
        "id": "789012",
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "status": "PENDING"
      }
    ],
    "total": 2
  }
  ```

### Get All Groups
- **Endpoint**: `/api/admin/groups`
- **Method**: GET
- **Description**: Get all groups (admin only)
- **Headers**: Authorization: Bearer {token}
- **Response**: 
  ```json
  {
    "groups": [
      {
        "id": "G12345",
        "name": "Awesome Tribe",
        "memberCount": 4,
        "status": "ACTIVE"
      },
      {
        "id": "G67890",
        "name": "Cool Tribe",
        "memberCount": 3,
        "status": "FORMING"
      }
    ],
    "total": 2
  }
  ```

### Run Matching Algorithm
- **Endpoint**: `/api/admin/match/run`
- **Method**: POST
- **Description**: Manually run the matching algorithm (admin only)
- **Headers**: Authorization: Bearer {token}
- **Response**: 
  ```json
  {
    "success": true,
    "message": "Matching algorithm started",
    "jobId": "J12345"
  }
  ```
