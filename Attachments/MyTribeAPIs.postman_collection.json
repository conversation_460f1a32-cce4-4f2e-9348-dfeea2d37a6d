{"info": {"_postman_id": "734fe50f-902d-4f38-8ba5-ef7e7d469b5c", "name": "MyTribeAPIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "1227284"}, "item": [{"name": "New User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"zip\": \"75001\",\n    \"gender\": \"male\",\n    \"interest\": \"chess\",\n    \"availableTimeSlots\": [\"THURSDAY_7PM\"]\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8085/match-user", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["match-user"]}, "description": "Generated from cURL: curl -X POST http://localhost:8085/match-user \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"zip\": \"75001\",\n    \"gender\": \"male\",\n    \"interest\": \"chess\",\n    \"availableTimeSlots\": [\"THURSDAY_7PM\"]\n  }'\n"}, "response": []}, {"name": "Exit User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"zip\": \"75001\",\n    \"gender\": \"male\"\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8085/exit-user", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["exit-user"]}, "description": "Generated from cURL: curl -X POST http://localhost:8085/exit-user \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"zip\": \"75001\",\n    \"gender\": \"male\"\n  }'\n"}, "response": []}, {"name": "System-status", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8085/system-status", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["system-status"]}}, "response": []}, {"name": "Render-Sys-status", "request": {"method": "GET", "header": [], "url": {"raw": "https://mytribe-api.onrender.com/system-status", "protocol": "https", "host": ["mytribe-api", "onrender", "com"], "path": ["system-status"]}, "description": "Generated from cURL: curl https://mytribe-api.onrender.com/system-status\n"}, "response": []}, {"name": "User Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"gender\": \"Male\",\n    \"profession\": \"Software Engineer\",\n    \"ethnicity\": \"Asian\",\n    \"bio\": \"Just moved to town, looking to meet interesting people.\",\n    \"employmentStatus\": \"Employed\",\n    \"relationshipStatus\": \"Single\",\n    \"zipCode\": \"94102\",\n    \"locationEnabled\": true\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8085/api/users/register", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "users", "register"]}, "description": "Generated from cURL: curl -X POST http://localhost:8085/api/users/register \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"name\": \"<PERSON>\",\n    \"gender\": \"Male\",\n    \"profession\": \"Software Engineer\",\n    \"ethnicity\": \"Asian\",\n    \"bio\": \"Just moved to town, looking to meet interesting people.\",\n    \"employmentStatus\": \"Employed\",\n    \"relationshipStatus\": \"Single\",\n    \"zipCode\": \"94102\",\n    \"locationEnabled\": true\n  }'\n"}, "response": []}, {"name": "Upload Photo", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/full/path/to/photo.jpg"}]}, "url": {"raw": "http://localhost:8085/api/users/74e14fe1-5d17-4156-bd53-d71b5cc17af4/photo", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "users", "74e14fe1-5d17-4156-bd53-d71b5cc17af4", "photo"]}, "description": "Generated from cURL: curl -X POST http://localhost:8085/api/users/74e14fe1-5d17-4156-bd53-d71b5cc17af4/photo \\\n  -H \"Content-Type: multipart/form-data\" \\\n  -F \"file=@/full/path/to/photo.jpg\"\n"}, "response": []}, {"name": "Upload-all-photos", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "profilePhoto", "type": "file", "src": "/path/to/profile.jpg"}, {"key": "selfie<PERSON><PERSON><PERSON>", "type": "file", "src": "/path/to/selfie.jpg"}, {"key": "idCardPhoto", "type": "file", "src": "/path/to/idcard.jpg"}]}, "url": {"raw": "http://localhost:8085/api/users/12345/upload-all-photos", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "users", "12345", "upload-all-photos"]}, "description": "Generated from cURL: curl -X POST http://localhost:8085/api/users/12345/upload-all-photos \\\n  -F \"profilePhoto=@/path/to/profile.jpg\" \\\n  -F \"selfiePhoto=@/path/to/selfie.jpg\" \\\n  -F \"idCardPhoto=@/path/to/idcard.jpg\"\n"}, "response": []}]}