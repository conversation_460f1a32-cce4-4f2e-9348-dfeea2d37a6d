# MyTribe

A social application that helps users find and form groups based on location, demographics, and shared interests. Users are matched into groups of 4 people for social activities and events.

## Tech Stack

**Backend:** Java Spring Boot, H2 Database, JWT Authentication
**Frontend:** React, Material-UI, Axios
**Testing:** Je<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Swagger

## Quick Start

### 🚀 Run Locally

**Backend (Terminal 1):**
```bash
mvn spring-boot:run
```

**Frontend (Terminal 2):**
```bash
cd frontend
npm install
npm start
```

### 🗄 Access the In-Memory Database (H2)

To inspect data stored during runtime:

1. Start the backend using `mvn spring-boot:run`
2. Open your browser and go to: [http://localhost:8085/h2-console](http://localhost:8085/h2-console)
3. Use the following credentials:
    - **JDBC URL:** `jdbc:h2:mem:mytribe`
    - **Username:** `sa`
    - **Password:** *(leave blank)*
4. Click "Connect" — you'll see all tables like `AUTH_USERS`, `USERS`, etc.

**Note:** This is an in-memory database. All data is lost when the app is stopped.

### 🌐 Access Points

- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8085
- **H2 Database:** http://localhost:8085/h2-console
- **Swagger Docs:** http://localhost:8085/swagger-ui/index.html

### 🧪 Quick Test

```bash
# Test all APIs
bash docs/curl/run-all-tests.sh
```

## 📚 Documentation

Detailed documentation is organized in the `docs/` folder:

### 👤 User Experience
- [**User Flow**](docs/user-flow/) - Complete user journey from signup to payment
- [**Admin Dashboard**](docs/admin/) - Admin features and validation

### 🔧 Development
- [**API Documentation**](docs/api/) - All endpoints with examples
- [**Testing Guide**](docs/testing/) - Unit, integration, and E2E tests
- [**Deployment**](docs/deployment/) - Local and production setup

### 🧪 API Testing
- [**Curl Scripts**](docs/curl/) - Ready-to-run API test scripts

## 🚀 Features

- **Authentication:** JWT-based signup/login with phone verification
- **User Profiles:** Multi-step registration with photo upload
- **Smart Matching:** Location and preference-based group formation
- **Group Management:** Real-time status tracking and member management
- **Payment System:** Secure payment processing
- **Admin Dashboard:** System monitoring and analytics
- **Responsive UI:** Material-UI with light/dark themes

## 🛠 Prerequisites

- **Java 11+**
- **Maven 3.6+**
- **Node.js 16+**
- **npm/yarn**

## 📝 License

Proprietary software for MyTribe application.
