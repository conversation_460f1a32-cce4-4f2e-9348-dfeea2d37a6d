# Automated Testing System for MyTribe Frontend

This directory contains scripts to automatically generate and run tests for the MyTribe frontend application.

## Overview

The automated testing system:

1. Scans the codebase for React components
2. Generates appropriate test files for each component
3. Runs the tests to verify they pass
4. Keeps track of progress and continues with the next component

## Scripts

### Main Scripts

- `start-automated-testing.sh`: Starts the automated testing process in the background
- `stop-automated-testing.sh`: Stops the automated testing process
- `check-test-status.sh`: Checks the status of the automated testing process

### Supporting Files

- `test-generator.js`: Scans the codebase and generates test files
- `test-templates.js`: Contains templates for different types of component tests
- `auto-test-runner.sh`: The main script that runs tests and updates progress
- `test-progress.json`: Keeps track of which tests have been completed and which are next

## How to Use

### Starting the Automated Testing

```bash
cd frontend
./start-automated-testing.sh
```

This will start the automated testing process in the background. The process will:

1. Find all components in your codebase
2. Generate appropriate tests for each component
3. Run the tests to see if they pass
4. Move on to the next component if the tests pass

### Checking the Status

```bash
cd frontend
./check-test-status.sh
```

This will show:
- Whether the automated testing process is running
- How long it has been running
- Progress information (completed tests, current test, remaining tests)
- Recent log entries
- Test results summary

### Stopping the Automated Testing

```bash
cd frontend
./stop-automated-testing.sh
```

This will stop the automated testing process.

## Logs

All logs are stored in the `logs` directory:

- `logs/auto_test_*.log`: Main logs from the automated testing process
- `logs/test_output_*.log`: Output from the test runs

## Troubleshooting

### Process Crashes

If the process crashes, you can check the logs to see what happened:

```bash
cd frontend
ls -la logs/
tail -100 logs/auto_test_*.log | less
```

### Cleaning Up

If you need to start fresh:

```bash
cd frontend
./stop-automated-testing.sh
rm test-progress.json
rm -rf logs/
```

### Resuming After Interruption

The system keeps track of progress in `test-progress.json`. If the process is interrupted, you can simply restart it and it will continue from where it left off:

```bash
cd frontend
./start-automated-testing.sh
```
