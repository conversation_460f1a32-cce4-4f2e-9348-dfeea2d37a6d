# MyTribe Frontend

This is the frontend for the MyTribe application, built with React and Material-UI.

## Features

- User authentication (signup, login)
- User profile management
- Group matching based on location and interests
- Group status viewing
- Admin dashboard for system monitoring

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn

### Installation

1. Install dependencies:

```bash
npm install
# or
yarn install
```

2. Start the development server:

```bash
npm start
# or
yarn start
```

The application will be available at http://localhost:3000.

## Project Structure

- `/src/components/auth`: Authentication components (Login, Signup)
- `/src/components/profile`: User profile components
- `/src/components/groups`: Group matching and status components
- `/src/components/admin`: Admin dashboard components
- `/src/components/common`: Shared components (Navbar, Footer, etc.)
- `/src/services`: API service classes for backend communication

## Backend Integration

The frontend is configured to communicate with the Spring Boot backend. The proxy is set up in `package.json` to forward API requests to the backend server running on port 8085.

## Available Scripts

- `npm start`: Starts the development server
- `npm build`: Builds the app for production
- `npm test`: Runs tests
- `npm eject`: Ejects from Create React App

## Deployment

To build the application for production:

```bash
npm run build
# or
yarn build
```

This will create a `build` directory with optimized production files.
