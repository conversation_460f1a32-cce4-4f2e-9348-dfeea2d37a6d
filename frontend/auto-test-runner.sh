#!/bin/bash

# Set the working directory to the frontend folder
cd "$(dirname "$0")"

# Create logs directory if it doesn't exist
mkdir -p logs

# Set up log files with timestamps
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
MAIN_LOG="logs/auto_test_${TIMESTAMP}.log"
TEST_OUTPUT_LOG="logs/test_output_${TIMESTAMP}.log"

# Function to log messages
log_message() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message" | tee -a "$MAIN_LOG"
}

# Function to run tests and check if they pass
run_tests() {
  log_message "Running tests..."
  npm test -- --watchAll=false > "$TEST_OUTPUT_LOG" 2>&1
  TEST_EXIT_CODE=$?

  # Display the test output
  cat "$TEST_OUTPUT_LOG"

  # Return the exit code from the test run
  return $TEST_EXIT_CODE
}

# Function to generate the next test
generate_next_test() {
  log_message "Generating next test..."
  node test-generator.js 2>&1 | tee -a "$MAIN_LOG"
  return ${PIPESTATUS[0]}
}

# Function to update progress
update_progress() {
  log_message "Updating progress..."
  node -e "try {
    const fs=require('fs');
    const progress=JSON.parse(fs.readFileSync('test-progress.json'));
    if(progress.currentTest) {
      progress.completedTests.push(progress.currentTest);
      progress.currentTest = progress.remainingTests.shift();
      fs.writeFileSync('test-progress.json', JSON.stringify(progress, null, 2));
      console.log('Progress updated successfully. New current test: ' + progress.currentTest);
    } else {
      console.log('No current test to update.');
    }
  } catch(e) {
    console.error('Error updating progress:', e);
  }" 2>&1 | tee -a "$MAIN_LOG"
  return ${PIPESTATUS[0]}
}

# Check if required files exist
if [ ! -f package.json ]; then
  log_message "ERROR: package.json not found. Make sure you're running this script from the frontend directory."
  exit 1
fi

# Initialize progress file if it doesn't exist
if [ ! -f test-progress.json ]; then
  log_message "Initializing progress file..."
  echo '{"completedTests": [], "currentTest": null, "remainingTests": []}' > test-progress.json
fi

# Check if node_modules exists
if [ ! -d node_modules ]; then
  log_message "Installing dependencies..."
  npm install
fi

# Print startup message
log_message "=== Automated Test Runner Started ==="
log_message "Main log: $MAIN_LOG"
log_message "Test output log: $TEST_OUTPUT_LOG"

# Set a counter for consecutive failures
FAILURE_COUNT=0
MAX_FAILURES=3

# Main loop
while true; do
  # Generate the next test if needed
  generate_next_test
  GEN_RESULT=$?

  if [ $GEN_RESULT -ne 0 ]; then
    log_message "WARNING: Test generation failed with exit code $GEN_RESULT"
    FAILURE_COUNT=$((FAILURE_COUNT + 1))

    if [ $FAILURE_COUNT -ge $MAX_FAILURES ]; then
      log_message "ERROR: Too many consecutive failures. Exiting."
      exit 1
    fi

    log_message "Waiting 30 seconds before trying again..."
    sleep 30
    continue
  fi

  # Run the tests
  run_tests
  TEST_RESULT=$?

  # Check if tests passed
  if [ $TEST_RESULT -eq 0 ]; then
    log_message "Tests passed! Moving to next test..."
    FAILURE_COUNT=0

    # Update progress file - mark current test as completed and move to next
    update_progress

    # Sleep for a bit before continuing
    sleep 5
  else
    log_message "Tests failed with exit code $TEST_RESULT. Please check the errors."
    FAILURE_COUNT=$((FAILURE_COUNT + 1))

    if [ $FAILURE_COUNT -ge $MAX_FAILURES ]; then
      log_message "ERROR: Too many consecutive failures. Exiting."
      exit 1
    fi

    log_message "Waiting 30 seconds before trying again..."
    sleep 30
  fi
done
