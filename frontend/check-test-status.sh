#!/bin/bash

# Set the working directory to the frontend folder
cd "$(dirname "$0")"

# Check if the auto-test-runner is running
if [ -f .test-runner.pid ]; then
  PID=$(cat .test-runner.pid)
  
  if ps -p $PID > /dev/null; then
    echo "Auto test runner is running with PID $PID"
    
    # Check how long it's been running
    if [ "$(uname)" == "Darwin" ]; then
      # macOS
      START_TIME=$(ps -p $PID -o lstart= | xargs -I{} date -jf "%a %b %d %T %Y" "{}" "+%s")
      CURRENT_TIME=$(date +%s)
      RUNTIME=$((CURRENT_TIME - START_TIME))
      
      # Format runtime
      HOURS=$((RUNTIME / 3600))
      MINUTES=$(( (RUNTIME % 3600) / 60 ))
      SECONDS=$((RUNTIME % 60))
      
      echo "Running for: ${HOURS}h ${MINUTES}m ${SECONDS}s"
    else
      # Linux
      ELAPSED=$(ps -o etime= -p $PID)
      echo "Running for: $ELAPSED"
    fi
  else
    echo "Process with PID $PID is not running, but PID file exists."
    echo "The process may have crashed or been terminated improperly."
    echo "You can clean up by removing the PID file: rm .test-runner.pid"
  fi
else
  # Try to find the process by name
  PID=$(pgrep -f "auto-test-runner.sh")
  
  if [ -z "$PID" ]; then
    echo "Auto test runner is not running."
  else
    echo "Auto test runner is running with PID $PID, but no PID file exists."
    echo "You can create the PID file with: echo $PID > .test-runner.pid"
  fi
fi

# Check progress
if [ -f test-progress.json ]; then
  echo -e "\nTest Progress:"
  echo "=============="
  
  # Use node to parse and display the progress
  node -e "
    try {
      const fs = require('fs');
      const progress = JSON.parse(fs.readFileSync('test-progress.json'));
      
      console.log('Completed tests:', progress.completedTests.length);
      console.log('Current test:', progress.currentTest || 'None');
      console.log('Remaining tests:', progress.remainingTests.length);
      
      if (progress.completedTests.length > 0) {
        console.log('\nLast 5 completed tests:');
        const lastCompleted = progress.completedTests.slice(-5);
        lastCompleted.forEach(test => console.log('- ' + test));
      }
      
      if (progress.remainingTests.length > 0) {
        console.log('\nNext 5 tests in queue:');
        const nextTests = progress.remainingTests.slice(0, 5);
        nextTests.forEach(test => console.log('- ' + test));
      }
    } catch (e) {
      console.error('Error reading progress file:', e.message);
    }
  "
else
  echo -e "\nNo progress file found. Tests haven't started yet."
fi

# Check the latest log files
echo -e "\nLatest Logs:"
echo "============"

# Find the most recent log file
LATEST_LOG=$(ls -t logs/auto_test_*.log 2>/dev/null | head -1)

if [ -n "$LATEST_LOG" ]; then
  echo "Latest log file: $LATEST_LOG"
  echo -e "\nLast 10 log entries:"
  tail -10 "$LATEST_LOG"
else
  echo "No log files found."
fi

# Check test results
echo -e "\nTest Results:"
echo "============="

# Count test files
TEST_COUNT=$(find src/__tests__ -name "*.test.js" 2>/dev/null | wc -l)
echo "Total test files: $TEST_COUNT"

# Show the latest test output
LATEST_TEST_OUTPUT=$(ls -t logs/test_output_*.log 2>/dev/null | head -1)

if [ -n "$LATEST_TEST_OUTPUT" ]; then
  echo "Latest test output: $LATEST_TEST_OUTPUT"
  
  # Try to extract test results
  PASSED=$(grep -o "[0-9]* passed" "$LATEST_TEST_OUTPUT" | tail -1)
  FAILED=$(grep -o "[0-9]* failed" "$LATEST_TEST_OUTPUT" | tail -1)
  
  if [ -n "$PASSED" ] || [ -n "$FAILED" ]; then
    echo "Last test run: $PASSED, $FAILED"
  else
    echo "Could not determine test results from the latest output."
  fi
else
  echo "No test output files found."
fi
