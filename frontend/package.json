{"name": "mytribe-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "axios": "^1.4.0", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-material-ui-carousel": "^3.4.2", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:e2e": "react-scripts test --testMatch='**/tests/e2e/**/*.test.js' --watchAll=false", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8085", "devDependencies": {"@testing-library/user-event": "^14.6.1"}}