import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import MatchService from '../../services/match.service';
import MatchingTab from './MatchingTab';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import GroupsIcon from '@mui/icons-material/Groups';
import PersonIcon from '@mui/icons-material/Person';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import BarChartIcon from '@mui/icons-material/BarChart';
import PieChartIcon from '@mui/icons-material/PieChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import AssessmentIcon from '@mui/icons-material/Assessment';
import WorkIcon from '@mui/icons-material/Work';
import CakeIcon from '@mui/icons-material/Cake';
import WcIcon from '@mui/icons-material/Wc';
import PublicIcon from '@mui/icons-material/Public';

const AdminDashboard = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [systemData, setSystemData] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [userDetailsOpen, setUserDetailsOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Mock data for demonstration
  const mockSystemData = {
    stats: {
      totalUsers: 127,
      totalGroups: 18,
      completeGroups: 12,
      inProgressGroups: 6,
      unmatchedUsers: 43,
      maleUsers: 68,
      femaleUsers: 59
    },
    groups: [
      {
        id: 'group-123',
        gender: 'male',
        isFinalized: true,
        memberCount: 4,
        centerLocation: 'New York, NY',
        createdAt: '2023-07-15T14:30:00Z'
      },
      {
        id: 'group-456',
        gender: 'female',
        isFinalized: true,
        memberCount: 4,
        centerLocation: 'Brooklyn, NY',
        createdAt: '2023-07-16T10:15:00Z'
      },
      {
        id: 'group-789',
        gender: 'male',
        isFinalized: false,
        memberCount: 3,
        centerLocation: 'Queens, NY',
        createdAt: '2023-07-17T09:45:00Z'
      },
      {
        id: 'group-101',
        gender: 'female',
        isFinalized: false,
        memberCount: 2,
        centerLocation: 'Manhattan, NY',
        createdAt: '2023-07-18T16:20:00Z'
      }
    ],
    users: [
      {
        id: 'user-123',
        name: 'John Doe',
        gender: 'male',
        location: 'New York, NY',
        zip: '10001',
        groupId: 'group-123',
        status: 'matched',
        registeredAt: '2023-07-10T08:30:00Z'
      },
      {
        id: 'user-456',
        name: 'Jane Smith',
        gender: 'female',
        location: 'Brooklyn, NY',
        zip: '11201',
        groupId: 'group-456',
        status: 'matched',
        registeredAt: '2023-07-11T14:45:00Z'
      },
      {
        id: 'user-789',
        name: 'Mike Johnson',
        gender: 'male',
        location: 'Queens, NY',
        zip: '11101',
        groupId: 'group-789',
        status: 'in-progress',
        registeredAt: '2023-07-12T11:20:00Z'
      },
      {
        id: 'user-101',
        name: 'Sarah Williams',
        gender: 'female',
        location: 'Manhattan, NY',
        zip: '10016',
        groupId: null,
        status: 'waiting',
        registeredAt: '2023-07-13T09:10:00Z'
      }
    ]
  };

  // Function to generate system data from local storage
  const generateLocalSystemData = () => {
    // Get users from local storage
    const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');

    // Get groups from local storage
    const localGroups = JSON.parse(localStorage.getItem('localGroups') || '[]');

    // Categorize groups
    const completeGroups = localGroups.filter(group => group.isFinalized);
    const inProgressGroups = localGroups.filter(group => !group.isFinalized);

    // Find unmatched users (users not in any group)
    const usersInGroups = localGroups.flatMap(group => group.members.map(member => member.id));
    const unmatchedUsers = localUsers.filter(user => !usersInGroups.includes(user.userId))
      .map(user => ({
        id: user.userId,
        name: user.username || user.profile?.name || `User-${user.userId.substring(0, 5)}`,
        gender: user.profile?.gender || 'unknown',
        zip: user.profile?.zipCode || '00000',
        city: 'Unknown',
        state: 'US',
        hobby: user.profile?.interests?.[0] || 'general',
        availableTimeSlots: user.profile?.availableTimeSlots || []
      }));

    // Create data object that mimics the API response
    const data = {
      totalMatchedUsers: usersInGroups.length,
      totalUsersWaiting: unmatchedUsers.length,
      completeGroups: completeGroups.map(group => ({
        id: group.id,
        gender: group.gender || 'mixed',
        members: group.members
      })),
      inProgressGroups: inProgressGroups.map(group => ({
        id: group.id,
        gender: group.gender || 'mixed',
        members: group.members
      })),
      unmatchedUsers: unmatchedUsers
    };

    // Format the data for the dashboard
    return {
      stats: {
        totalUsers: data.totalMatchedUsers + data.totalUsersWaiting,
        totalGroups: data.completeGroups.length + data.inProgressGroups.length,
        completeGroups: data.completeGroups.length,
        inProgressGroups: data.inProgressGroups.length,
        unmatchedUsers: data.unmatchedUsers.length,
        maleUsers: countUsersByGender(data, 'male'),
        femaleUsers: countUsersByGender(data, 'female')
      },
      groups: [
        ...formatGroups(data.completeGroups, true),
        ...formatGroups(data.inProgressGroups, false)
      ],
      users: [
        ...formatUsers(data.unmatchedUsers, 'waiting'),
        ...formatUsersFromGroups(data.inProgressGroups, 'in-progress'),
        ...formatUsersFromGroups(data.completeGroups, 'matched')
      ]
    };
  };

  // Function to fetch system data
  const fetchSystemData = async () => {
    try {
      // Get system status from the backend
      const response = await MatchService.getSystemStatus();
      const data = response.data;

      // Format the data for the dashboard
      const formattedData = {
        stats: {
          totalUsers: data.totalMatchedUsers + data.totalUsersWaiting,
          totalGroups: data.inProgressGroups.length + data.completeGroups.length,
          completeGroups: data.completeGroups.length,
          inProgressGroups: data.inProgressGroups.length,
          unmatchedUsers: data.unmatchedUsers.length,
          maleUsers: countUsersByGender(data, 'male'),
          femaleUsers: countUsersByGender(data, 'female')
        },
        groups: [
          ...formatGroups(data.completeGroups, true),
          ...formatGroups(data.inProgressGroups, false)
        ],
        users: [
          ...formatUsers(data.unmatchedUsers, 'waiting'),
          ...formatUsersFromGroups(data.inProgressGroups, 'in-progress'),
          ...formatUsersFromGroups(data.completeGroups, 'matched')
        ]
      };

      setSystemData(formattedData);
    } catch (err) {
      console.error('Error fetching system data:', err);
      setError('Failed to fetch system data. Using local data instead.');

      // Generate system data from local storage
      const localSystemData = generateLocalSystemData();
      setSystemData(localSystemData);
    } finally {
      setLoading(false);
    }
  };

  // Fetch system data on component mount
  useEffect(() => {
    fetchSystemData();
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Helper function to count users by gender
  const countUsersByGender = (data, gender) => {
    const unmatchedCount = data.unmatchedUsers.filter(user => user.gender === gender).length;

    const inProgressCount = data.inProgressGroups.reduce((count, group) => {
      return count + group.members.filter(member => member.gender === gender).length;
    }, 0);

    const completeCount = data.completeGroups.reduce((count, group) => {
      return count + group.members.filter(member => member.gender === gender).length;
    }, 0);

    return unmatchedCount + inProgressCount + completeCount;
  };

  // Helper function to format groups for display
  const formatGroups = (groups, isFinalized) => {
    return groups.map(group => ({
      id: group.id,
      gender: group.gender,
      isFinalized: isFinalized,
      memberCount: group.members.length,
      centerLocation: `${group.members[0]?.city || 'Unknown'}, ${group.members[0]?.state || 'Unknown'}`,
      createdAt: new Date().toISOString() // Backend doesn't provide creation date yet
    }));
  };

  // Helper function to format unmatched users
  const formatUsers = (users, status) => {
    return users.map(user => ({
      id: user.id,
      name: user.name || `User-${user.id.substring(0, 5)}`,
      gender: user.gender,
      location: `${user.city || 'Unknown'}, ${user.state || 'Unknown'}`,
      zip: user.zip,
      groupId: null,
      status: status,
      registeredAt: new Date().toISOString() // Backend doesn't provide registration date yet
    }));
  };

  // Helper function to format users from groups
  const formatUsersFromGroups = (groups, status) => {
    return groups.flatMap(group => {
      return group.members.map(user => ({
        id: user.id,
        name: user.name || `User-${user.id.substring(0, 5)}`,
        gender: user.gender,
        location: `${user.city || 'Unknown'}, ${user.state || 'Unknown'}`,
        zip: user.zip,
        groupId: group.id,
        status: status,
        registeredAt: new Date().toISOString() // Backend doesn't provide registration date yet
      }));
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Admin action handlers
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  const [groupDetailsOpen, setGroupDetailsOpen] = useState(false);
  const [groupActionLoading, setGroupActionLoading] = useState(false);
  const [groupActionError, setGroupActionError] = useState('');
  const [groupActionSuccess, setGroupActionSuccess] = useState('');

  // Handler for viewing group details
  const handleViewGroupDetails = (groupId) => {
    setSelectedGroupId(groupId);
    setGroupDetailsOpen(true);
  };

  // Handler for viewing user details
  const handleViewUserDetails = (userId) => {
    const user = systemData.users.find(u => u.id === userId);
    if (user) {
      setSelectedUser(user);
      setUserDetailsOpen(true);
    }
  };

  // Handler for finalizing a group
  const handleFinalizeGroup = async (groupId) => {
    setGroupActionLoading(true);
    setGroupActionError('');
    setGroupActionSuccess('');

    try {
      // In a real implementation, this would call a backend API
      // For now, we'll simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the local state to reflect the change
      setSystemData(prevData => {
        const updatedGroups = prevData.groups.map(group =>
          group.id === groupId ? { ...group, isFinalized: true } : group
        );

        return {
          ...prevData,
          groups: updatedGroups,
          stats: {
            ...prevData.stats,
            completeGroups: prevData.stats.completeGroups + 1,
            inProgressGroups: prevData.stats.inProgressGroups - 1
          }
        };
      });

      setGroupActionSuccess(`Group ${groupId} has been finalized successfully.`);
    } catch (err) {
      console.error('Error finalizing group:', err);
      setGroupActionError(`Failed to finalize group ${groupId}. Please try again.`);
    } finally {
      setGroupActionLoading(false);
    }
  };

  // Handler for disbanding a group
  const handleDisbandGroup = async (groupId) => {
    setGroupActionLoading(true);
    setGroupActionError('');
    setGroupActionSuccess('');

    try {
      // In a real implementation, this would call a backend API
      // For now, we'll simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the local state to reflect the change
      setSystemData(prevData => {
        const groupToRemove = prevData.groups.find(group => group.id === groupId);
        const isFinalized = groupToRemove?.isFinalized || false;

        // Remove the group from the list
        const updatedGroups = prevData.groups.filter(group => group.id !== groupId);

        // Update the stats
        const updatedStats = {
          ...prevData.stats,
          totalGroups: prevData.stats.totalGroups - 1
        };

        if (isFinalized) {
          updatedStats.completeGroups = prevData.stats.completeGroups - 1;
        } else {
          updatedStats.inProgressGroups = prevData.stats.inProgressGroups - 1;
        }

        // Move users to unmatched
        const usersFromGroup = prevData.users.filter(user => user.groupId === groupId);
        const updatedUsers = prevData.users.map(user =>
          user.groupId === groupId ? { ...user, groupId: null, status: 'waiting' } : user
        );

        updatedStats.unmatchedUsers = prevData.stats.unmatchedUsers + usersFromGroup.length;

        return {
          ...prevData,
          groups: updatedGroups,
          users: updatedUsers,
          stats: updatedStats
        };
      });

      setGroupActionSuccess(`Group ${groupId} has been disbanded successfully.`);
    } catch (err) {
      console.error('Error disbanding group:', err);
      setGroupActionError(`Failed to disband group ${groupId}. Please try again.`);
    } finally {
      setGroupActionLoading(false);
    }
  };

  // Handler for running the matching algorithm
  const handleRunMatchingAlgorithm = async () => {
    setGroupActionLoading(true);
    setGroupActionError('');
    setGroupActionSuccess('');

    try {
      // In a real implementation, this would call a backend API
      // For now, we'll simulate the API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      setGroupActionSuccess('Matching algorithm executed successfully. New groups have been formed.');

      // Refresh the data
      fetchSystemData();
    } catch (err) {
      console.error('Error running matching algorithm:', err);
      setGroupActionError('Failed to run matching algorithm. Please try again.');
    } finally {
      setGroupActionLoading(false);
    }
  };

  // Handler for removing a user from a group
  const handleRemoveUserFromGroup = async (userId, groupId) => {
    setGroupActionLoading(true);
    setGroupActionError('');
    setGroupActionSuccess('');

    try {
      // In a real implementation, this would call a backend API
      // For now, we'll simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the local state to reflect the change
      setSystemData(prevData => {
        // Get the group
        const group = prevData.groups.find(g => g.id === groupId);
        if (!group) throw new Error(`Group ${groupId} not found`);

        // Check if the group is finalized
        if (group.isFinalized) {
          throw new Error('Cannot remove users from finalized groups');
        }

        // Update the user's status
        const updatedUsers = prevData.users.map(user =>
          user.id === userId ? { ...user, groupId: null, status: 'waiting' } : user
        );

        // Update the group's member count
        const updatedGroups = prevData.groups.map(g =>
          g.id === groupId ? { ...g, memberCount: g.memberCount - 1 } : g
        );

        // Update stats
        const updatedStats = {
          ...prevData.stats,
          unmatchedUsers: prevData.stats.unmatchedUsers + 1
        };

        return {
          ...prevData,
          groups: updatedGroups,
          users: updatedUsers,
          stats: updatedStats
        };
      });

      setGroupActionSuccess(`User ${userId} has been removed from group ${groupId}.`);
    } catch (err) {
      console.error('Error removing user from group:', err);
      setGroupActionError(err.message || `Failed to remove user ${userId} from group ${groupId}.`);
    } finally {
      setGroupActionLoading(false);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading admin dashboard...
        </Typography>
      </Container>
    );
  }

  // Render error state
  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="contained"
          onClick={() => {
            setLoading(true);
            setError('');
            fetchSystemData();
          }}
        >
          Try Again
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Admin Dashboard
        </Typography>
        <Button
          variant="contained"
          color="secondary"
          component={Link}
          to="/admin/api-test"
        >
          API Testing Dashboard
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Users
              </Typography>
              <Typography variant="h4">
                {systemData.stats.totalUsers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Groups
              </Typography>
              <Typography variant="h4">
                {systemData.stats.totalGroups}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {systemData.stats.completeGroups} complete, {systemData.stats.inProgressGroups} in progress
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Unmatched Users
              </Typography>
              <Typography variant="h4">
                {systemData.stats.unmatchedUsers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Gender Distribution
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h5">
                    {systemData.stats.maleUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Male
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="h5">
                    {systemData.stats.femaleUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Female
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="Groups" icon={<GroupsIcon />} iconPosition="start" />
          <Tab label="Users" icon={<PersonIcon />} iconPosition="start" />
          <Tab label="Matching" icon={<PeopleAltIcon />} iconPosition="start" />
          <Tab label="Market Analysis" icon={<BarChartIcon />} iconPosition="start" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <Paper sx={{ p: 3 }}>
        {/* Groups Tab */}
        {tabValue === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              All Groups
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Group ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Gender</TableCell>
                    <TableCell>Members</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {systemData.groups
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((group) => (
                      <TableRow key={group.id}>
                        <TableCell>{group.id}</TableCell>
                        <TableCell>
                          <Chip
                            icon={group.isFinalized ? <CheckCircleIcon /> : <HourglassEmptyIcon />}
                            label={group.isFinalized ? "Complete" : "In Progress"}
                            color={group.isFinalized ? "success" : "primary"}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{group.gender}</TableCell>
                        <TableCell>{group.memberCount}/4</TableCell>
                        <TableCell>{group.centerLocation}</TableCell>
                        <TableCell>{formatDate(group.createdAt)}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button size="small" variant="outlined" onClick={() => handleViewGroupDetails(group.id)}>
                              View Details
                            </Button>
                            {!group.isFinalized && (
                              <Button
                                size="small"
                                variant="outlined"
                                color="secondary"
                                onClick={() => handleFinalizeGroup(group.id)}
                              >
                                Finalize
                              </Button>
                            )}
                            <Button
                              size="small"
                              variant="outlined"
                              color="error"
                              onClick={() => handleDisbandGroup(group.id)}
                            >
                              Disband
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={systemData.groups.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </Box>
        )}

        {/* Users Tab */}
        {tabValue === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              All Users
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User ID</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Gender</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Registered</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {systemData.users
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>{user.id}</TableCell>
                        <TableCell>{user.name}</TableCell>
                        <TableCell>{user.gender}</TableCell>
                        <TableCell>{user.location}</TableCell>
                        <TableCell>
                          <Chip
                            label={
                              user.status === 'matched'
                                ? 'Matched'
                                : user.status === 'in-progress'
                                  ? 'In Progress'
                                  : 'Waiting'
                            }
                            color={
                              user.status === 'matched'
                                ? 'success'
                                : user.status === 'in-progress'
                                  ? 'primary'
                                  : 'default'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{formatDate(user.registeredAt)}</TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleViewUserDetails(user.id)}
                          >
                            View Profile
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={systemData.users.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </Box>
        )}

        {/* Matching Tab */}
        {tabValue === 2 && (
          <MatchingTab
            systemData={systemData}
            onDataChange={() => {
              setLoading(true);
              setTimeout(() => {
                fetchSystemData();
              }, 1000);
            }}
          />
        )}

        {/* Market Analysis Tab */}
        {tabValue === 3 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              User Demographics & Insights
            </Typography>

            <Grid container spacing={3}>
              {/* Age Distribution */}
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <CakeIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">Age Distribution</Typography>
                  </Box>
                  <Box sx={{ height: 250, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 80 }}>18-24</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '35%', bgcolor: '#3f51b5', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>35%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 80 }}>25-30</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '45%', bgcolor: '#3f51b5', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>45%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 80 }}>31-35</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '15%', bgcolor: '#3f51b5', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>15%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ width: 80 }}>36+</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '5%', bgcolor: '#3f51b5', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>5%</Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>

              {/* Gender Distribution */}
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <WcIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">Gender Distribution</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center', height: 250 }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Box sx={{
                        width: 120,
                        height: 120,
                        borderRadius: '50%',
                        bgcolor: '#3f51b5',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mb: 1
                      }}>
                        <Typography variant="h4" sx={{ color: 'white' }}>55%</Typography>
                      </Box>
                      <Typography variant="body1">Female</Typography>
                      <Typography variant="body2" color="text.secondary">{systemData.stats.femaleUsers} users</Typography>
                    </Box>
                    <Box sx={{ textAlign: 'center' }}>
                      <Box sx={{
                        width: 120,
                        height: 120,
                        borderRadius: '50%',
                        bgcolor: '#f50057',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mb: 1
                      }}>
                        <Typography variant="h4" sx={{ color: 'white' }}>45%</Typography>
                      </Box>
                      <Typography variant="body1">Male</Typography>
                      <Typography variant="body2" color="text.secondary">{systemData.stats.maleUsers} users</Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>

              {/* Profession Distribution */}
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <WorkIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">Top Professions</Typography>
                  </Box>
                  <Box sx={{ height: 250, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Software Engineer</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '30%', bgcolor: '#4caf50', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>30%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Marketing</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '20%', bgcolor: '#4caf50', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>20%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Finance</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '15%', bgcolor: '#4caf50', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>15%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Healthcare</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '12%', bgcolor: '#4caf50', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>12%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Other</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '23%', bgcolor: '#4caf50', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>23%</Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>

              {/* Ethnicity Distribution */}
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PublicIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">Ethnicity Distribution</Typography>
                  </Box>
                  <Box sx={{ height: 250, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 150 }}>Asian</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '35%', bgcolor: '#ff9800', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>35%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 150 }}>White/Caucasian</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '25%', bgcolor: '#ff9800', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>25%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 150 }}>Black/African American</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '15%', bgcolor: '#ff9800', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>15%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ width: 150 }}>Hispanic/Latino</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '18%', bgcolor: '#ff9800', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>18%</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ width: 150 }}>Other</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '7%', bgcolor: '#ff9800', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>7%</Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            </Grid>

            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
              Group Formation Insights
            </Typography>

            <Grid container spacing={3}>
              {/* Group Formation Rate */}
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <TimelineIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">Group Formation Rate</Typography>
                  </Box>
                  <Box sx={{ height: 250, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Last 7 days</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '15%', bgcolor: '#9c27b0', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>15 groups</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Last 30 days</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '45%', bgcolor: '#9c27b0', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>45 groups</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ width: 120 }}>Last 90 days</Typography>
                      <Box sx={{ flexGrow: 1, bgcolor: 'rgba(0,0,0,0.1)', borderRadius: 1, height: 20, position: 'relative' }}>
                        <Box sx={{ width: '85%', bgcolor: '#9c27b0', height: '100%', borderRadius: 1 }} />
                      </Box>
                      <Typography variant="body2" sx={{ ml: 1 }}>85 groups</Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>

              {/* Group Completion Rate */}
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AssessmentIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">Group Completion Rate</Typography>
                  </Box>
                  <Box sx={{ height: 250, display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
                    <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
                      <CircularProgress variant="determinate" value={75} size={150} thickness={5} />
                      <Box
                        sx={{
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          position: 'absolute',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="h4" component="div">
                          75%
                        </Typography>
                      </Box>
                    </Box>
                    <Typography variant="body1" sx={{ textAlign: 'center' }}>
                      75% of groups reach the full 4 members
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 1 }}>
                      Average time to completion: 14 days
                    </Typography>
                  </Box>
                </Paper>
              </Grid>

              {/* User Retention */}
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PeopleAltIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">User Retention & Engagement</Typography>
                  </Box>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h3" color="primary">87%</Typography>
                        <Typography variant="body1">Complete their profile</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h3" color="primary">65%</Typography>
                        <Typography variant="body1">Join a group</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h3" color="primary">92%</Typography>
                        <Typography variant="body1">Attend first meetup</Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>

      {/* Group Details Dialog */}
      <Dialog
        open={groupDetailsOpen}
        onClose={() => setGroupDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Group Details
          {selectedGroupId && (
            <Typography variant="subtitle2" color="text.secondary">
              ID: {selectedGroupId}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent dividers>
          {selectedGroupId && systemData && (
            <>
              {/* Group Information */}
              <Typography variant="h6" gutterBottom>Group Information</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Status</Typography>
                    <Typography variant="body1">
                      {systemData.groups.find(g => g.id === selectedGroupId)?.isFinalized ?
                        <Chip color="success" size="small" label="Complete" icon={<CheckCircleIcon />} /> :
                        <Chip color="warning" size="small" label="In Progress" icon={<HourglassEmptyIcon />} />}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Gender</Typography>
                    <Typography variant="body1">
                      {systemData.groups.find(g => g.id === selectedGroupId)?.gender || 'Unknown'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Member Count</Typography>
                    <Typography variant="body1">
                      {systemData.users.filter(user => user.groupId === selectedGroupId).length} / 4
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Location</Typography>
                    <Typography variant="body1">
                      {systemData.groups.find(g => g.id === selectedGroupId)?.centerLocation || 'Unknown'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Created</Typography>
                    <Typography variant="body1">
                      {formatDate(systemData.groups.find(g => g.id === selectedGroupId)?.createdAt || new Date())}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Last Updated</Typography>
                    <Typography variant="body1">
                      {formatDate(systemData.groups.find(g => g.id === selectedGroupId)?.updatedAt || new Date())}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* Event Details */}
              <Typography variant="h6" gutterBottom>Event Details</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Venue</Typography>
                    <Typography variant="body1">
                      {systemData.groups.find(g => g.id === selectedGroupId)?.venue || 'Central Park Cafe'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Address</Typography>
                    <Typography variant="body1">
                      {systemData.groups.find(g => g.id === selectedGroupId)?.address || '123 Main St, New York, NY'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Date & Time</Typography>
                    <Typography variant="body1">
                      {systemData.groups.find(g => g.id === selectedGroupId)?.date || 'March 25, 2025'} at {systemData.groups.find(g => g.id === selectedGroupId)?.time || '3:00 PM'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="primary">Event Status</Typography>
                    <Typography variant="body1">
                      {systemData.groups.find(g => g.id === selectedGroupId)?.isFinalized ?
                        <Chip color="success" size="small" label="Confirmed" /> :
                        <Chip color="warning" size="small" label="Pending" />}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* Group Insights */}
              <Typography variant="h6" gutterBottom>Group Insights</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Common Interests</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      {['Hiking', 'Reading', 'Music', 'Travel'].map(interest => (
                        <Chip key={interest} label={interest} size="small" />
                      ))}
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Availability Match</Typography>
                    <Typography variant="body1">
                      85% of members have matching availability
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Matching Criteria Used</Typography>
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Box component="span" sx={{ width: 120 }}>Location:</Box>
                        <Chip size="small" label="Within 5 miles" />
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Box component="span" sx={{ width: 120 }}>Gender:</Box>
                        <Chip size="small" label="Same gender" />
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Box component="span" sx={{ width: 120 }}>Age Range:</Box>
                        <Chip size="small" label="25-35" />
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box component="span" sx={{ width: 120 }}>Interests:</Box>
                        <Chip size="small" label="At least 2 common" />
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Group Compatibility Score</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                        <CircularProgress variant="determinate" value={75} />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="caption" component="div" color="text.secondary">
                            75%
                          </Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2">
                        This group has a high compatibility score based on interests, location, and availability.
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>

              {/* Group Members */}
              <Typography variant="h6" gutterBottom>Group Members</Typography>
              <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>User ID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Gender</TableCell>
                      <TableCell>Location</TableCell>
                      <TableCell>Distance from Center</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {systemData.users
                      .filter(user => user.groupId === selectedGroupId)
                      .map(user => (
                        <TableRow key={user.id}>
                          <TableCell>{user.id}</TableCell>
                          <TableCell>
                            <Button
                              color="primary"
                              onClick={() => handleViewUserDetails(user.id)}
                              sx={{ textTransform: 'none', fontWeight: 'bold' }}
                            >
                              {user.name}
                            </Button>
                          </TableCell>
                          <TableCell>{user.gender}</TableCell>
                          <TableCell>{user.location}</TableCell>
                          <TableCell>
                            <Chip
                              size="small"
                              label={`${Math.floor(Math.random() * 5) + 1} miles`}
                              color={Math.random() > 0.5 ? "success" : "warning"}
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={() => handleViewUserDetails(user.id)}
                              >
                                Details
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                color="error"
                                onClick={() => handleRemoveUserFromGroup(user.id, selectedGroupId)}
                              >
                                Remove
                              </Button>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Admin Actions */}
              <Typography variant="h6" gutterBottom>Admin Actions</Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                {!systemData.groups.find(g => g.id === selectedGroupId)?.isFinalized && (
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={() => {
                      handleFinalizeGroup(selectedGroupId);
                      setGroupDetailsOpen(false);
                    }}
                  >
                    Finalize Group
                  </Button>
                )}
                <Button
                  variant="contained"
                  color="error"
                  onClick={() => {
                    handleDisbandGroup(selectedGroupId);
                    setGroupDetailsOpen(false);
                  }}
                >
                  Disband Group
                </Button>
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setGroupDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* User Details Dialog */}
      <Dialog
        open={userDetailsOpen}
        onClose={() => setUserDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          User Details
          {selectedUser && (
            <Typography variant="subtitle2" color="text.secondary">
              ID: {selectedUser.id}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent dividers>
          {selectedUser && (
            <>
              {/* User Information */}
              <Typography variant="h6" gutterBottom>Basic Information</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Name</Typography>
                    <Typography variant="body1">
                      {selectedUser.name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Gender</Typography>
                    <Typography variant="body1">
                      {selectedUser.gender}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Location</Typography>
                    <Typography variant="body1">
                      {selectedUser.location}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">ZIP Code</Typography>
                    <Typography variant="body1">
                      {selectedUser.zip || 'Not provided'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Group Status</Typography>
                    <Typography variant="body1">
                      {selectedUser.groupId ? (
                        <Chip
                          color="success"
                          size="small"
                          label="In Group"
                          onClick={() => {
                            setUserDetailsOpen(false);
                            setSelectedGroupId(selectedUser.groupId);
                            setGroupDetailsOpen(true);
                          }}
                        />
                      ) : (
                        <Chip color="error" size="small" label="Unmatched" />
                      )}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Typography variant="subtitle2" color="primary">Registered</Typography>
                    <Typography variant="body1">
                      {formatDate(selectedUser.registeredAt)}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* User Profile */}
              <Typography variant="h6" gutterBottom>Profile Information</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Interests</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      {['Hiking', 'Reading', 'Music', 'Travel'].map(interest => (
                        <Chip key={interest} label={interest} size="small" />
                      ))}
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Availability</Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', mt: 1 }}>
                      <Typography variant="body2">• Thursday 7:00 PM</Typography>
                      <Typography variant="body2">• Friday 7:00 PM</Typography>
                      <Typography variant="body2">• Saturday 11:00 AM</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="primary">Bio</Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* Matching Information */}
              <Typography variant="h6" gutterBottom>Matching Information</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Compatibility Score</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                        <CircularProgress variant="determinate" value={82} />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="caption" component="div" color="text.secondary">
                            82%
                          </Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2">
                        High compatibility with current group members
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="primary">Location Match</Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Within 5 miles of group center
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Action Feedback */}
      {groupActionSuccess && (
        <Alert
          severity="success"
          sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 9999 }}
          onClose={() => setGroupActionSuccess('')}
        >
          {groupActionSuccess}
        </Alert>
      )}

      {groupActionError && (
        <Alert
          severity="error"
          sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 9999 }}
          onClose={() => setGroupActionError('')}
        >
          {groupActionError}
        </Alert>
      )}
    </Container>
  );
};

export default AdminDashboard;
