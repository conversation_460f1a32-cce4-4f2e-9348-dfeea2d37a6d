import React, { useEffect, useRef, useContext } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { ThemeContext } from '../../contexts/ThemeContext';

const AnimatedBackground = () => {
  const canvasRef = useRef(null);
  const theme = useTheme();
  const { mode, colorTheme } = useContext(ThemeContext);

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let particles = [];

    // Get colors based on theme
    const getColors = () => {
      const primaryColor = theme.palette.primary.main;
      const secondaryColor = theme.palette.secondary.main;
      const accentColor = colorTheme === 'blue' ? '#5c6bc0' :
                          colorTheme === 'yellow' ? '#ffb300' :
                          '#9e9e9e';

      return [
        primaryColor,
        secondaryColor,
        accentColor,
        mode === 'dark' ? '#ffffff' : '#000000'
      ];
    };

    // Initialize particles
    function initParticles() {
      particles = [];
      const colors = getColors();
      const particleCount = Math.min(Math.floor(window.innerWidth / 10), 100);

      for (let i = 0; i < particleCount; i++) {
        const size = Math.random() * 5 + 1;
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          speedX: Math.random() * 1 - 0.5,
          speedY: Math.random() * 1 - 0.5,
          size: size,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.5 + 0.1,
          blurAmount: Math.random() * 2
        });
      }
    }

    // Set canvas dimensions
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      initParticles();
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    // Animation loop
    function animate() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particles.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;

        // Bounce off edges
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.speedX *= -1;
        }

        if (particle.y < 0 || particle.y > canvas.height) {
          particle.speedY *= -1;
        }

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = alpha(particle.color, particle.opacity);
        ctx.shadowColor = particle.color;
        ctx.shadowBlur = particle.blurAmount;
        ctx.fill();
        ctx.closePath();
      });

      // Draw connections between nearby particles
      particles.forEach((particle, index) => {
        for (let j = index + 1; j < particles.length; j++) {
          const dx = particle.x - particles[j].x;
          const dy = particle.y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            ctx.beginPath();
            ctx.strokeStyle = alpha(particle.color, 0.05 * (1 - distance / 100));
            ctx.lineWidth = 0.5;
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
            ctx.closePath();
          }
        }
      });

      animationFrameId = window.requestAnimationFrame(animate);
    }

    // Start animation
    animate();

    // Cleanup
    return () => {
      window.cancelAnimationFrame(animationFrameId);
      window.removeEventListener('resize', handleResize);
    };
  }, [theme, mode, colorTheme]);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        opacity: 0.7,
        pointerEvents: 'none'
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          display: 'block',
          width: '100%',
          height: '100%'
        }}
      />
    </Box>
  );
};

export default AnimatedBackground;
