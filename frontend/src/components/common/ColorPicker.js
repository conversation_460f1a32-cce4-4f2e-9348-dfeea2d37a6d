import React, { useState } from 'react';
import { Box, TextField, Popover, useTheme, alpha } from '@mui/material';
import { SketchPicker } from 'react-color';

const ColorPicker = ({ color, onChange, disabled = false }) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    if (!disabled) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChange = (newColor) => {
    onChange(newColor.hex);
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}
      >
        <Box
          onClick={handleClick}
          sx={{
            width: 36,
            height: 36,
            borderRadius: 1,
            bgcolor: color,
            cursor: disabled ? 'not-allowed' : 'pointer',
            border: `1px solid ${theme.palette.divider}`,
            opacity: disabled ? 0.5 : 1,
            transition: 'all 0.2s',
            '&:hover': {
              transform: disabled ? 'none' : 'scale(1.05)',
              boxShadow: disabled ? 'none' : `0 0 0 2px ${alpha(theme.palette.primary.main, 0.3)}`
            }
          }}
        />
        <TextField
          value={color}
          size="small"
          disabled={disabled}
          InputProps={{
            readOnly: true,
            sx: {
              cursor: disabled ? 'not-allowed' : 'pointer',
              '&:hover': {
                bgcolor: disabled ? 'transparent' : alpha(theme.palette.primary.main, 0.05)
              }
            }
          }}
          onClick={handleClick}
          sx={{ flexGrow: 1 }}
        />
      </Box>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={{
          '& .MuiPopover-paper': {
            boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
            borderRadius: 2,
            mt: 1
          }
        }}
      >
        <SketchPicker
          color={color}
          onChange={handleChange}
          disableAlpha
        />
      </Popover>
    </>
  );
};

export default ColorPicker;
