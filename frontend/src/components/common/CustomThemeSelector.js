import React, { useContext, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Switch,
  FormControlLabel,
  Tooltip,
  useTheme,
  alpha,
  Button,
  Stack,
  Snackbar,
  Alert
} from '@mui/material';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { ThemeContext } from '../../contexts/ThemeContext';
import ColorPicker from './ColorPicker';

const CustomThemeSelector = ({ onClose }) => {
  const theme = useTheme();
  const {
    customColors,
    setCustomColor,
    applyCustomTheme,
    toggleCustomTheme,
    resetCustomColors,
    setColorTheme
  } = useContext(ThemeContext);

  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  return (
    <Paper
      elevation={3}
      sx={{
        p: 3,
        borderRadius: 2,
        mb: 3,
        background: theme.palette.mode === 'dark'
          ? alpha(theme.palette.background.paper, 0.8)
          : alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(10px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
      }}
    >
      <Typography
        variant="h6"
        gutterBottom
        sx={{
          fontWeight: 600,
          mb: 2,
          display: 'flex',
          alignItems: 'center',
          '&::after': {
            content: '""',
            display: 'block',
            height: 2,
            flexGrow: 1,
            backgroundColor: alpha(theme.palette.primary.main, 0.2),
            ml: 2,
            borderRadius: 1
          }
        }}
      >
        Customize Theme Colors
      </Typography>

      <FormControlLabel
        control={
          <Switch
            checked={applyCustomTheme}
            onChange={toggleCustomTheme}
            color="primary"
          />
        }
        label={
          <Typography variant="body1" sx={{ fontWeight: 500 }}>
            {applyCustomTheme ? 'Using Custom Colors' : 'Use Custom Colors'}
          </Typography>
        }
        sx={{ mb: 2 }}
      />

      <Grid container spacing={3}>
        <Grid item xs={12} sm={4}>
          <Tooltip title="Change the background color of the app">
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Background Color
              </Typography>
              <ColorPicker
                color={customColors.background}
                onChange={(color) => setCustomColor('background', color)}
                disabled={!applyCustomTheme}
              />
            </Box>
          </Tooltip>
        </Grid>

        <Grid item xs={12} sm={4}>
          <Tooltip title="Change the color of buttons">
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Button Color
              </Typography>
              <ColorPicker
                color={customColors.buttons}
                onChange={(color) => setCustomColor('buttons', color)}
                disabled={!applyCustomTheme}
              />
            </Box>
          </Tooltip>
        </Grid>

        <Grid item xs={12} sm={4}>
          <Tooltip title="Change the color of boxes and cards">
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Box Color
              </Typography>
              <ColorPicker
                color={customColors.boxes}
                onChange={(color) => setCustomColor('boxes', color)}
                disabled={!applyCustomTheme}
              />
            </Box>
          </Tooltip>
        </Grid>
      </Grid>

      {applyCustomTheme && (
        <Box sx={{ mt: 2, p: 2, borderRadius: 1, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
          <Typography variant="body2">
            Your custom theme is now active. You can adjust the colors above to see changes in real-time.
          </Typography>
        </Box>
      )}

      <Stack direction="row" spacing={2} sx={{ mt: 3, justifyContent: 'space-between' }}>
        <Button
          variant="outlined"
          color="error"
          startIcon={<RestartAltIcon />}
          onClick={() => {
            resetCustomColors();
            setSnackbarMessage('Theme reset to default colors');
            setSnackbarOpen(true);
          }}
        >
          Reset to Default
        </Button>

        <Button
          variant="contained"
          color="primary"
          startIcon={<CheckCircleOutlineIcon />}
          onClick={() => {
            // Set the theme to custom and enable it
            setColorTheme('custom');
            if (!applyCustomTheme) {
              toggleCustomTheme();
            }

            // Show success message
            setSnackbarMessage('Custom theme applied successfully');
            setSnackbarOpen(true);

            // Close the dialog after a short delay
            setTimeout(() => {
              if (onClose) onClose();
            }, 500);
          }}
        >
          Apply & Close
        </Button>
      </Stack>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity="success"
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default CustomThemeSelector;
