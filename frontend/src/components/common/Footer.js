import React, { useContext } from 'react';
import { Box, Container, Typography, Link, Grid, Divider, useTheme, IconButton } from '@mui/material';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import InstagramIcon from '@mui/icons-material/Instagram';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import { ThemeContext } from '../../contexts/ThemeContext';

const Footer = () => {
  const theme = useTheme();
  const { mode } = useContext(ThemeContext);

  return (
    <Box
      component="footer"
      sx={{
        py: 6,
        px: 2,
        mt: 'auto',
        backgroundColor: mode === 'light'
          ? theme.palette.grey[100]
          : theme.palette.grey[900],
        borderTop: `1px solid ${mode === 'light' ? 'rgba(0,0,0,0.05)' : 'rgba(255,255,255,0.05)'}`,
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          <Grid item xs={12} sm={4}>
            <Typography variant="h6" color="primary" gutterBottom sx={{ fontWeight: 600 }}>
              MyTribe
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 300 }}>
              Connect with like-minded people in your area based on location and shared interests.
            </Typography>
            <Box sx={{ mt: 3 }}>
              <IconButton color="primary" aria-label="Facebook" size="small">
                <FacebookIcon />
              </IconButton>
              <IconButton color="primary" aria-label="Twitter" size="small" sx={{ ml: 1 }}>
                <TwitterIcon />
              </IconButton>
              <IconButton color="primary" aria-label="Instagram" size="small" sx={{ ml: 1 }}>
                <InstagramIcon />
              </IconButton>
              <IconButton color="primary" aria-label="LinkedIn" size="small" sx={{ ml: 1 }}>
                <LinkedInIcon />
              </IconButton>
            </Box>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Typography variant="h6" color="primary" gutterBottom sx={{ fontWeight: 600 }}>
              Quick Links
            </Typography>
            <Link href="/" color="text.secondary" sx={{ display: 'block', mb: 1, textDecoration: 'none' }}>
              Home
            </Link>
            <Link href="/login" color="text.secondary" sx={{ display: 'block', mb: 1, textDecoration: 'none' }}>
              Login
            </Link>
            <Link href="/signup" color="text.secondary" sx={{ display: 'block', mb: 1, textDecoration: 'none' }}>
              Sign Up
            </Link>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Typography variant="h6" color="primary" gutterBottom sx={{ fontWeight: 600 }}>
              Legal
            </Typography>
            <Link href="#" color="text.secondary" sx={{ display: 'block', mb: 1, textDecoration: 'none' }}>
              Privacy Policy
            </Link>
            <Link href="#" color="text.secondary" sx={{ display: 'block', mb: 1, textDecoration: 'none' }}>
              Terms of Service
            </Link>
            <Link href="#" color="text.secondary" sx={{ display: 'block', mb: 1, textDecoration: 'none' }}>
              Cookie Policy
            </Link>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4 }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap' }}>
          <Typography variant="body2" color="text.secondary">
            © {new Date().getFullYear()} MyTribe. All rights reserved.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Made with ❤️ for connecting people
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
