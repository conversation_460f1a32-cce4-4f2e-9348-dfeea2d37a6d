import React, { useContext, useEffect, useRef } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import ImageGallery from './ImageGallery';
import {
  Box,
  Button,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Stack,
  useTheme,
  alpha,
  keyframes
} from '@mui/material';
import GroupsIcon from '@mui/icons-material/Groups';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import SecurityIcon from '@mui/icons-material/Security';
import { ThemeContext } from '../../contexts/ThemeContext';
import AnimatedBackground from './AnimatedBackground';

// Define animations
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

const float = keyframes`
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

const HomePage = () => {
  const theme = useTheme();
  const { colorTheme } = useContext(ThemeContext);
  const heroRef = useRef(null);
  return (
    <Box>
      {/* Animated Background */}
      <AnimatedBackground />
      {/* Hero Section */}
      <Box
        sx={{
          position: 'relative',
          bgcolor: 'primary.main',
          color: theme.palette.primary.contrastText,
          py: { xs: 6, md: 12 },
          borderRadius: 4,
          mb: 8,
          overflow: 'hidden',
          boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.4)} 0%, ${alpha(theme.palette.primary.dark, 0.6)} 100%)`,
            zIndex: 1
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '30%',
            background: `linear-gradient(to top, ${alpha(theme.palette.primary.dark, 0.8)} 0%, transparent 100%)`,
            zIndex: 1
          }
        }}
      >
        <Container maxWidth="md" sx={{ position: 'relative', zIndex: 2 }}>
          <Typography
            component="h1"
            variant="h2"
            align="center"
            gutterBottom
            sx={{
              fontWeight: 800,
              fontSize: { xs: '2.5rem', md: '3.75rem' },
              textShadow: '0 2px 10px rgba(0,0,0,0.2)',
              mb: 3,
              animation: `${fadeIn} 1s ease-out, ${float} 6s ease-in-out infinite`,
              background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Find Your Tribe
          </Typography>
          <Typography
            variant="h5"
            align="center"
            paragraph
            sx={{
              fontSize: { xs: '1.1rem', md: '1.5rem' },
              fontWeight: 400,
              maxWidth: '800px',
              mx: 'auto',
              mb: 4,
              lineHeight: 1.6,
              textShadow: '0 1px 5px rgba(0,0,0,0.1)',
              animation: `${fadeIn} 1.2s ease-out`,
              opacity: 0,
              animationFillMode: 'forwards',
              animationDelay: '0.3s'
            }}
          >
            Connect with like-minded people in your area. MyTribe matches you with others based on location and interests.
          </Typography>
          <Stack
            sx={{
              pt: 4,
              animation: `${fadeIn} 1.4s ease-out`,
              opacity: 0,
              animationFillMode: 'forwards',
              animationDelay: '0.6s'
            }}
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            justifyContent="center"
          >
            <Button
              variant="contained"
              color="secondary"
              component={RouterLink}
              to="/signup"
              size="large"
              sx={{
                py: 1.5,
                px: 4,
                fontSize: '1.1rem',
                fontWeight: 600,
                boxShadow: '0 4px 14px rgba(0,0,0,0.2)',
                animation: `${pulse} 3s infinite ease-in-out`,
                '&:hover': {
                  boxShadow: '0 6px 20px rgba(0,0,0,0.3)',
                  transform: 'translateY(-2px)',
                  animation: 'none'
                },
                transition: 'all 0.2s ease'
              }}
            >
              Join Now
            </Button>
            <Button
              variant="outlined"
              component={RouterLink}
              to="/login"
              size="large"
              sx={{
                py: 1.5,
                px: 4,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.5)',
                color: 'white',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: 'rgba(255,255,255,0.1)'
                }
              }}
            >
              Sign In
            </Button>
          </Stack>
        </Container>
      </Box>

      {/* Features Section */}
      <Container sx={{ py: { xs: 6, md: 10 } }} maxWidth="lg">
        <Typography
          variant="h4"
          align="center"
          gutterBottom
          sx={{
            fontWeight: 700,
            fontSize: { xs: '1.75rem', md: '2.5rem' },
            mb: 5,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -16,
              left: '50%',
              transform: 'translateX(-50%)',
              width: 80,
              height: 4,
              borderRadius: 2,
              backgroundColor: 'primary.main'
            }
          }}
        >
          How It Works
        </Typography>
        <Grid container spacing={4} sx={{ mt: 4 }}>
          <Grid item xs={12} sm={4}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease',
                animation: `${fadeIn} 0.8s ease-out`,
                opacity: 0,
                animationFillMode: 'forwards',
                animationDelay: '0.2s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 30px rgba(0,0,0,0.1)'
                }
              }}
            >
              <CardMedia
                component="div"
                sx={{
                  pt: '56.25%',
                  bgcolor: 'primary.light',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.7)} 0%, ${alpha(theme.palette.primary.main, 0.9)} 100%)`,
                    zIndex: 1
                  }
                }}
              >
                <GroupsIcon sx={{
                  fontSize: 80,
                  color: 'white',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 2,
                  filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.2))'
                }} />
              </CardMedia>
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                <Typography
                  gutterBottom
                  variant="h5"
                  component="h2"
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    color: 'primary.main'
                  }}
                >
                  Join a Group
                </Typography>
                <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                  Get matched with a group of 4 people who share your interests and live nearby.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease',
                animation: `${fadeIn} 0.8s ease-out`,
                opacity: 0,
                animationFillMode: 'forwards',
                animationDelay: '0.4s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 30px rgba(0,0,0,0.1)'
                }
              }}
            >
              <CardMedia
                component="div"
                sx={{
                  pt: '56.25%',
                  bgcolor: 'secondary.light',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.light, 0.7)} 0%, ${alpha(theme.palette.secondary.main, 0.9)} 100%)`,
                    zIndex: 1
                  }
                }}
              >
                <LocationOnIcon sx={{
                  fontSize: 80,
                  color: 'white',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 2,
                  filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.2))'
                }} />
              </CardMedia>
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                <Typography
                  gutterBottom
                  variant="h5"
                  component="h2"
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    color: 'secondary.main'
                  }}
                >
                  Local Connections
                </Typography>
                <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                  Meet people within 10 miles of your location for convenient meetups.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease',
                animation: `${fadeIn} 0.8s ease-out`,
                opacity: 0,
                animationFillMode: 'forwards',
                animationDelay: '0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 30px rgba(0,0,0,0.1)'
                }
              }}
            >
              <CardMedia
                component="div"
                sx={{
                  pt: '56.25%',
                  bgcolor: 'primary.dark',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.7)} 0%, ${alpha(theme.palette.primary.dark, 0.9)} 100%)`,
                    zIndex: 1
                  }
                }}
              >
                <SecurityIcon sx={{
                  fontSize: 80,
                  color: 'white',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 2,
                  filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.2))'
                }} />
              </CardMedia>
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                <Typography
                  gutterBottom
                  variant="h5"
                  component="h2"
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    color: 'primary.dark'
                  }}
                >
                  Safe Community
                </Typography>
                <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                  Verified profiles and secure matching ensure a safe experience for all members.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>

      {/* Image Gallery */}
      <Container sx={{ py: 8 }}>
        <ImageGallery variant="default" />
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          position: 'relative',
          bgcolor: 'secondary.light',
          py: { xs: 8, md: 12 },
          borderRadius: 4,
          mt: 8,
          overflow: 'hidden',
          boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.light, 0.4)} 0%, ${alpha(theme.palette.secondary.main, 0.6)} 100%)`,
            zIndex: 1
          }
        }}
      >
        <Container maxWidth="md" sx={{ position: 'relative', zIndex: 2 }}>
          <Typography
            variant="h3"
            align="center"
            gutterBottom
            sx={{
              fontWeight: 700,
              fontSize: { xs: '2rem', md: '3rem' },
              mb: 3,
              color: theme.palette.mode === 'dark' ? 'white' : 'black',
              textShadow: '0 2px 10px rgba(0,0,0,0.1)',
              animation: `${fadeIn} 0.8s ease-out, ${float} 6s ease-in-out infinite`,
              animationDelay: '0.2s'
            }}
          >
            Ready to find your tribe?
          </Typography>
          <Typography
            variant="h6"
            align="center"
            paragraph
            sx={{
              fontSize: { xs: '1.1rem', md: '1.25rem' },
              maxWidth: '700px',
              mx: 'auto',
              mb: 5,
              color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.7)',
            }}
          >
            Sign up today and start connecting with people who share your interests.
          </Typography>
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            mt: 4,
            animation: `${fadeIn} 0.8s ease-out`,
            opacity: 0,
            animationFillMode: 'forwards',
            animationDelay: '0.6s'
          }}>
            <Button
              variant="contained"
              color="primary"
              size="large"
              component={RouterLink}
              to="/signup"
              sx={{
                py: 2,
                px: 6,
                fontSize: '1.2rem',
                fontWeight: 600,
                borderRadius: 3,
                boxShadow: '0 4px 14px rgba(0,0,0,0.2)',
                animation: `${pulse} 3s infinite ease-in-out`,
                '&:hover': {
                  boxShadow: '0 6px 20px rgba(0,0,0,0.3)',
                  transform: 'translateY(-2px)',
                  animation: 'none'
                },
                transition: 'all 0.2s ease'
              }}
            >
              Get Started
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
