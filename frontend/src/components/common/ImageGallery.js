import React from 'react';
import { Box, Paper, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';

const ImageGallery = ({ variant = 'default' }) => {
  const theme = useTheme();
  
  // Define different image layouts based on variant
  const layouts = {
    default: {
      title: 'Find Your Tribe',
      subtitle: 'Connect with like-minded people and share great moments together',
      images: [
        { src: '/images/group-eating-1.jpg', alt: 'Group of friends eating and laughing together' },
        { src: '/images/group-eating-2.jpg', alt: 'People sharing a meal and having fun' },
      ],
      direction: 'row',
    },
    profile: {
      title: 'Join the Community',
      subtitle: 'Complete your profile to find your perfect tribe',
      images: [
        { src: '/images/group-eating-3.jpg', alt: 'Friends enjoying food together' },
      ],
      direction: 'column',
    },
    group: {
      title: 'Your Tribe Awaits',
      subtitle: 'Great conversations and memories are just around the corner',
      images: [
        { src: '/images/group-eating-1.jpg', alt: 'Group of friends eating and laughing together' },
        { src: '/images/group-eating-3.jpg', alt: 'Friends enjoying food together' },
      ],
      direction: 'row',
    },
  };
  
  const currentLayout = layouts[variant] || layouts.default;
  
  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h5" gutterBottom align="center" sx={{ mt: 2 }}>
        {currentLayout.title}
      </Typography>
      <Typography variant="subtitle1" gutterBottom align="center" color="text.secondary" sx={{ mb: 3 }}>
        {currentLayout.subtitle}
      </Typography>
      
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: currentLayout.direction,
          gap: 2,
          flexWrap: 'wrap',
          justifyContent: 'center'
        }}
      >
        {currentLayout.images.map((image, index) => (
          <Paper 
            key={index} 
            elevation={3}
            sx={{ 
              overflow: 'hidden',
              borderRadius: 2,
              width: currentLayout.direction === 'row' ? { xs: '100%', sm: '45%' } : '100%',
              height: currentLayout.direction === 'row' ? { xs: 200, sm: 300 } : 300,
              transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
              '&:hover': {
                transform: 'scale(1.02)',
                boxShadow: theme.shadows[6],
              }
            }}
          >
            <img 
              src={image.src} 
              alt={image.alt}
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'cover',
                display: 'block'
              }}
            />
          </Paper>
        ))}
      </Box>
    </Box>
  );
};

export default ImageGallery;
