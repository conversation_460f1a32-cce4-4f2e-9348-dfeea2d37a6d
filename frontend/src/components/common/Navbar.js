import React, { useState, useContext, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Button,
  Tooltip,
  MenuItem,
  Link,
  useTheme,
  Divider
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import GroupIcon from '@mui/icons-material/Group';
import ThemeSwitcher from './ThemeSwitcher';
import { ThemeContext } from '../../contexts/ThemeContext';

const Navbar = ({ isAuthenticated, onLogout, user }) => {
  const [anchorElNav, setAnchorElNav] = useState(null);
  const [anchorElUser, setAnchorElUser] = useState(null);
  const [isProfileComplete, setIsProfileComplete] = useState(false);

  // Check if profile is complete
  useEffect(() => {
    if (isAuthenticated && user) {
      // Check localStorage for profile data
      const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
      setIsProfileComplete(!!storedUser.profile);
    } else {
      setIsProfileComplete(false);
    }
  }, [isAuthenticated, user]);

  const handleOpenNavMenu = (event) => {
    setAnchorElNav(event.currentTarget);
  };

  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleLogout = () => {
    onLogout();
    handleCloseUserMenu();
  };

  // Navigation items based on authentication status and profile completion
  const navItems = isAuthenticated
    ? [
        { label: 'Profile', path: '/profile' },
        ...(isProfileComplete ? [{ label: 'My Tribe', path: '/group' }] : [])
      ]
    : [];

  // User menu items based on authentication status
  const userMenuItems = isAuthenticated
    ? [
        { label: 'Profile', path: '/profile' },
        { label: 'My Group', path: '/group' },
        { label: 'Logout', action: handleLogout },
      ]
    : [
        { label: 'Login', path: '/login' },
        { label: 'Sign Up', path: '/signup' },
      ];

  // Check if user is admin
  const isAdmin = user && user.role === 'ADMIN';
  if (isAdmin && isAuthenticated) {
    navItems.push({ label: 'Admin', path: '/admin' });
    userMenuItems.splice(2, 0, { label: 'Admin Dashboard', path: '/admin' });
  }

  return (
    <AppBar position="static">
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          {/* Logo for larger screens */}
          <GroupIcon sx={{ display: { xs: 'none', md: 'flex' }, mr: 1 }} />
          <Typography
            variant="h6"
            noWrap
            component={RouterLink}
            to={isAuthenticated ? "/profile" : "/"}
            sx={{
              mr: 2,
              display: { xs: 'none', md: 'flex' },
              fontFamily: 'monospace',
              fontWeight: 700,
              letterSpacing: '.3rem',
              color: 'inherit',
              textDecoration: 'none',
            }}
          >
            MyTribe
          </Typography>

          {/* Mobile menu */}
          <Box sx={{ flexGrow: 1, display: { xs: 'flex', md: 'none' } }}>
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleOpenNavMenu}
              color="inherit"
            >
              <MenuIcon />
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorElNav}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
              }}
              open={Boolean(anchorElNav)}
              onClose={handleCloseNavMenu}
              sx={{
                display: { xs: 'block', md: 'none' },
              }}
            >
              {navItems.map((item) => (
                <MenuItem key={item.label} onClick={handleCloseNavMenu}>
                  <Link
                    component={RouterLink}
                    to={item.path}
                    sx={{ textDecoration: 'none', color: 'text.primary' }}
                  >
                    <Typography textAlign="center">{item.label}</Typography>
                  </Link>
                </MenuItem>
              ))}
            </Menu>
          </Box>

          {/* Logo for mobile screens */}
          <GroupIcon sx={{ display: { xs: 'flex', md: 'none' }, mr: 1 }} />
          <Typography
            variant="h5"
            noWrap
            component={RouterLink}
            to={isAuthenticated ? "/profile" : "/"}
            sx={{
              mr: 2,
              display: { xs: 'flex', md: 'none' },
              flexGrow: 1,
              fontFamily: 'monospace',
              fontWeight: 700,
              letterSpacing: '.3rem',
              color: 'inherit',
              textDecoration: 'none',
            }}
          >
            MyTribe
          </Typography>

          {/* Desktop menu */}
          <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' } }}>
            {navItems.map((item) => (
              <Button
                key={item.label}
                component={RouterLink}
                to={item.path}
                onClick={handleCloseNavMenu}
                sx={{ my: 2, color: 'white', display: 'block' }}
              >
                {item.label}
              </Button>
            ))}
          </Box>

          {/* Theme Switcher */}
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
            <ThemeSwitcher />
          </Box>

          {/* User menu */}
          <Box sx={{ flexGrow: 0 }}>
            <Tooltip title="Open settings">
              <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                <Avatar alt={user ? user.name : 'Guest'} src="/static/images/avatar/2.jpg" />
              </IconButton>
            </Tooltip>
            <Menu
              sx={{ mt: '45px' }}
              id="menu-appbar"
              anchorEl={anchorElUser}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorElUser)}
              onClose={handleCloseUserMenu}
            >
              {isAuthenticated && user && (
                <Box sx={{ px: 2, py: 1, minWidth: 180 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    {user.username || 'User'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {user.email || ''}
                  </Typography>
                  <Divider sx={{ my: 1 }} />
                </Box>
              )}
              {userMenuItems.map((item) => (
                <MenuItem key={item.label} onClick={item.action || handleCloseUserMenu}>
                  {item.path ? (
                    <Link
                      component={RouterLink}
                      to={item.path}
                      sx={{ textDecoration: 'none', color: 'text.primary' }}
                    >
                      <Typography textAlign="center">{item.label}</Typography>
                    </Link>
                  ) : (
                    <Typography textAlign="center">{item.label}</Typography>
                  )}
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Navbar;
