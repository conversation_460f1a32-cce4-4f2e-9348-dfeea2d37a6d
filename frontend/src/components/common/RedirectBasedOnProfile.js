import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CircularProgress, Box, Typography } from '@mui/material';

const RedirectBasedOnProfile = ({ user }) => {
  const navigate = useNavigate();

  useEffect(() => {
    // Function to determine where to redirect the user
    const redirectUser = () => {
      // If no user data, redirect to login
      if (!user || !user.userId) {
        navigate('/login');
        return;
      }

      // For admin users, always go to admin dashboard
      if (user.role === 'ADMIN') {
        navigate('/admin');
        return;
      }

      // Get user data from localStorage to check profile status
      const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');
      const currentUser = localUsers.find(u => u.userId === user.userId);

      // Check if user has a complete profile
      if (currentUser && currentUser.profile && currentUser.profile.isComplete) {
        // Check if user is part of a group
        const localGroups = JSON.parse(localStorage.getItem('localGroups') || '[]');
        const userGroup = localGroups.find(group => 
          group.members && group.members.some(member => member.id === user.userId)
        );

        if (userGroup) {
          // If group has 4 members (complete), go to group summary
          if (userGroup.members.length === 4) {
            navigate('/group-summary');
          } else {
            // Otherwise go to group status
            navigate('/group-status');
          }
        } else {
          // User has profile but no group yet
          navigate('/group-preview');
        }
      } else {
        // User doesn't have a complete profile, go to profile page
        navigate('/profile');
      }
    };

    // Add a small delay to make the redirection feel more natural
    const timer = setTimeout(() => {
      redirectUser();
    }, 500);

    return () => clearTimeout(timer);
  }, [user, navigate]);

  return (
    <Box 
      sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        bgcolor: 'background.default'
      }}
    >
      <CircularProgress size={60} />
      <Typography variant="h6" sx={{ mt: 3 }}>
        Redirecting you to the right place...
      </Typography>
    </Box>
  );
};

export default RedirectBasedOnProfile;
