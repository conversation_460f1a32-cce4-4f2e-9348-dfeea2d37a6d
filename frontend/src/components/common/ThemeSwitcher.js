import React, { useContext, useState } from 'react';
import {
  Box,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Divider,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  useTheme,
  alpha
} from '@mui/material';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import PaletteIcon from '@mui/icons-material/Palette';
import CheckIcon from '@mui/icons-material/Check';
import TuneIcon from '@mui/icons-material/Tune';
import CustomThemeSelector from './CustomThemeSelector';
import { ThemeContext } from '../../contexts/ThemeContext';

const ThemeSwitcher = () => {
  const theme = useTheme();
  const { mode, colorTheme, toggleColorMode, setColorTheme, applyCustomTheme } = useContext(ThemeContext);
  const [anchorEl, setAnchorEl] = useState(null);
  const [customDialogOpen, setCustomDialogOpen] = useState(false);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleThemeChange = (theme) => {
    setColorTheme(theme);
    handleClose();
  };

  const handleOpenCustomDialog = () => {
    setCustomDialogOpen(true);
    handleClose();
  };

  const handleCloseCustomDialog = () => {
    setCustomDialogOpen(false);
  };

  // Color theme options with preview circles
  const colorThemes = [
    { name: 'blue', color: '#3f51b5', label: 'Blue' },
    { name: 'white', color: '#212121', label: 'Monochrome' },
    { name: 'yellow', color: '#ffc107', label: 'Yellow' },
    { name: 'custom', color: applyCustomTheme ? '#ff5722' : '#9e9e9e', label: 'Custom', isCustom: true },
  ];

  return (
    <Box>
      <Tooltip title="Theme settings">
        <IconButton
          onClick={handleClick}
          size="small"
          aria-controls={open ? 'theme-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          color="inherit"
        >
          <PaletteIcon />
        </IconButton>
      </Tooltip>
      <Menu
        id="theme-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'theme-button',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            minWidth: 200,
            borderRadius: 2,
            overflow: 'visible',
            mt: 1.5,
          },
        }}
      >
        <MenuItem onClick={toggleColorMode}>
          <ListItemIcon>
            {mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
          </ListItemIcon>
          <ListItemText>
            {mode === 'light' ? 'Dark Mode' : 'Light Mode'}
          </ListItemText>
        </MenuItem>

        <Divider />

        <Typography variant="caption" color="text.secondary" sx={{ px: 2, py: 1, display: 'block' }}>
          Color Theme
        </Typography>

        {colorThemes.map((theme) => (
          <MenuItem
            key={theme.name}
            onClick={theme.isCustom ? handleOpenCustomDialog : () => handleThemeChange(theme.name)}
            selected={colorTheme === theme.name}
          >
            <ListItemIcon>
              {theme.isCustom ? (
                <TuneIcon fontSize="small" color={applyCustomTheme ? "primary" : "inherit"} />
              ) : (
                <Box
                  sx={{
                    width: 20,
                    height: 20,
                    borderRadius: '50%',
                    bgcolor: theme.color,
                    border: '1px solid',
                    borderColor: 'divider'
                  }}
                />
              )}
            </ListItemIcon>
            <ListItemText>{theme.label}</ListItemText>
            {colorTheme === theme.name && (
              <CheckIcon fontSize="small" color="primary" sx={{ ml: 1 }} />
            )}
          </MenuItem>
        ))}
      </Menu>

      {/* Custom Theme Dialog */}
      <Dialog
        open={customDialogOpen}
        onClose={handleCloseCustomDialog}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
            background: theme.palette.mode === 'dark'
              ? alpha(theme.palette.background.paper, 0.9)
              : alpha(theme.palette.background.paper, 0.9),
            backdropFilter: 'blur(10px)',
          }
        }}
      >
        <DialogTitle sx={{
          pb: 1,
          fontWeight: 600,
          background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}>
          Customize Your Theme
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Select custom colors for your app theme. Changes will be applied in real-time.
          </Typography>
          <CustomThemeSelector onClose={handleCloseCustomDialog} />
        </DialogContent>

      </Dialog>
    </Box>
  );
};

export default ThemeSwitcher;
