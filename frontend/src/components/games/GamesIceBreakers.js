import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Container,
  Paper,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Stepper,
  Step,
  StepLabel,
  MobileStepper
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import { pageBackgroundStyle, mainCardStyle, headerWithBackStyle } from '../../styles/appStyles';

const GamesIceBreakers = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);

  // Sample games data
  const games = [
    {
      title: "Say 3 things about yourself – 2 true, 1 made up. Others guess the lie!",
      description: "A fun way to learn surprising facts about each other while testing how well you can spot a lie.",
      image: "https://images.unsplash.com/photo-1543269865-cbf427effbad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Z3JvdXAlMjBvZiUyMGZyaWVuZHN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60",
      examples: [
        {
          title: "Example 1",
          statements: [
            "I've met a Bollywood celeb.",
            "I hate pizza.",
            "I can ride a unicycle."
          ]
        },
        {
          title: "Example 2",
          statements: [
            "I speak 4 languages.",
            "I once got lost in an airport.",
            "I'm allergic to chocolate."
          ]
        }
      ],
      tip: "Make your lie tricky! 😉"
    },
    {
      title: "Desert Island",
      description: "If you were stranded on a desert island, what three items would you bring?",
      image: "https://images.unsplash.com/photo-1559128010-7c1ad6e1b6a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8ZGVzZXJ0JTIwaXNsYW5kfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60",
      examples: [
        {
          title: "Example",
          statements: [
            "A survival knife",
            "A photo of my family",
            "A book on edible plants"
          ]
        }
      ],
      tip: "Think about practical items, comfort items, and entertainment!"
    },
    {
      title: "Rapid Fire Questions",
      description: "Take turns asking each other quick questions to learn more about preferences and personalities.",
      image: "https://images.unsplash.com/photo-1573497491765-55a64cc0144c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTd8fHF1ZXN0aW9uc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60",
      examples: [
        {
          title: "Sample Questions",
          statements: [
            "Morning person or night owl?",
            "Beach or mountains?",
            "Coffee or tea?",
            "Favorite season?",
            "Dream vacation spot?"
          ]
        }
      ],
      tip: "Keep it light and fun! The quicker the answers, the better."
    }
  ];

  const handleBack = () => {
    navigate(-1);
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack2 = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleFinish = () => {
    navigate('/group-summary');
  };

  const maxSteps = games.length;
  const currentGame = games[activeStep];

  return (
    <Box sx={pageBackgroundStyle}>
      <Container maxWidth="sm" sx={{ py: 4 }}>
        <Paper sx={mainCardStyle}>
          {/* Header */}
          <Box sx={headerWithBackStyle}>
            <IconButton onClick={handleBack} sx={{ color: 'white' }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" component="h1">
              Games & Ice Breakers
            </Typography>
          </Box>

          {/* Game Content */}
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Box sx={{
              width: '100%',
              height: 180,
              borderRadius: 2,
              mb: 3,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              overflow: 'hidden',
              position: 'relative'
            }}>
              <img
                src={currentGame.image || "https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8ZnJpZW5kcyUyMGZ1bnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60"}
                alt={currentGame.title}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  borderRadius: '8px',
                  border: '2px solid #F5A623'
                }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = "https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8ZnJpZW5kcyUyMGZ1bnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60";
                }}
              />
            </Box>

            <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
              {currentGame.title}
            </Typography>

            <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
              {currentGame.description}
            </Typography>

            {currentGame.examples.map((example, index) => (
              <Card key={index} variant="outlined" sx={{ mb: 2, textAlign: 'left' }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    {example.title}
                  </Typography>
                  {example.statements.map((statement, idx) => (
                    <Typography key={idx} variant="body2" sx={{ mb: 1 }}>
                      {statement}
                    </Typography>
                  ))}
                </CardContent>
              </Card>
            ))}

            <Typography variant="body1" sx={{ mt: 2, fontWeight: 'bold' }}>
              {currentGame.tip}
            </Typography>

            <MobileStepper
              variant="dots"
              steps={maxSteps}
              position="static"
              activeStep={activeStep}
              sx={{
                maxWidth: 400,
                flexGrow: 1,
                mt: 3,
                mb: 2,
                bgcolor: 'transparent',
                '& .MuiMobileStepper-dot': {
                  bgcolor: 'rgba(255,255,255,0.3)'
                },
                '& .MuiMobileStepper-dotActive': {
                  bgcolor: '#F5A623'
                }
              }}
              nextButton={
                activeStep === maxSteps - 1 ? (
                  <Button
                    size="large"
                    onClick={handleFinish}
                    variant="contained"
                    fullWidth
                    sx={{
                      mt: 2,
                      bgcolor: '#F5A623',
                      color: 'black',
                      '&:hover': {
                        bgcolor: '#e69c1f'
                      }
                    }}
                  >
                    Back to Dashboard
                  </Button>
                ) : (
                  <Button
                    size="large"
                    onClick={handleNext}
                    variant="contained"
                    fullWidth
                    sx={{
                      mt: 2,
                      bgcolor: '#F5A623',
                      color: 'black',
                      '&:hover': {
                        bgcolor: '#e69c1f'
                      }
                    }}
                  >
                    Next
                  </Button>
                )
              }
              backButton={
                activeStep === 0 ? null : (
                  <Button
                    size="small"
                    onClick={handleBack2}
                    sx={{ color: 'rgba(255,255,255,0.7)' }}
                  >
                    <KeyboardArrowLeft />
                    Back
                  </Button>
                )
              }
            />
          </Box>

          {/* Bottom Navigation */}
          <Box sx={{ p: 2, textAlign: 'center', mt: 2 }}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-around',
                borderTop: '1px solid rgba(255,255,255,0.1)',
                pt: 2
              }}
            >
              <Button
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  color: 'rgba(255,255,255,0.7)'
                }}
                onClick={() => navigate('/group-summary')}
              >
                <HomeIcon />
                <Typography variant="caption">Home</Typography>
              </Button>

              <Button
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  color: 'rgba(255,255,255,0.7)'
                }}
              >
                <NotificationsIcon />
                <Typography variant="caption">Notifications</Typography>
              </Button>

              <Button
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  color: 'rgba(255,255,255,0.7)'
                }}
              >
                <SettingsIcon />
                <Typography variant="caption">Settings</Typography>
              </Button>
            </Box>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default GamesIceBreakers;
