import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  IconButton,
  Card,
  CardContent,
  Paper
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import appConfig from '../../config/appConfig';
import {
  pageBackgroundStyle,
  mainCardStyle,
  headerWithBackStyle,
  bottomNavStyle,
  homeButtonStyle,
  navButtonStyle,
  contentPaperStyle
} from '../../styles/appStyles';

const GroupFormationComplete = ({ user }) => {
  const navigate = useNavigate();

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle see event details
  const handleSeeEventDetails = () => {
    // Navigate to group summary
    navigate('/group-summary');
  };

  return (
    <Box sx={pageBackgroundStyle}>
      <Box sx={{ position: 'relative', zIndex: 2, width: '100%', maxWidth: 500, mx: 'auto' }}>
        <Card sx={mainCardStyle}>
          <CardContent sx={{ p: 0 }}>
            {/* Header */}
            <Box sx={headerWithBackStyle}>
              <IconButton onClick={handleBack} edge="start" sx={{ color: 'white' }}>
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="h6" component="h1" sx={{ fontWeight: 500, color: 'white' }}>
                Group Formation Complete!
              </Typography>
              <Box sx={{ width: 40 }} /> {/* Spacer for alignment */}
            </Box>

            {/* Content */}
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Paper
                elevation={0}
                sx={{
                  ...contentPaperStyle,
                  bgcolor: 'white',
                  color: 'text.primary',
                  textAlign: 'center',
                  py: 4
                }}
              >
                <Box
                  component="img"
                  src="https://img.icons8.com/color/96/000000/checked--v1.png"
                  alt="Payment Released"
                  sx={{
                    width: '80%',
                    maxWidth: 250,
                    height: 'auto',
                    mb: 3
                  }}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = "https://img.icons8.com/color/96/000000/checked--v1.png";
                  }}
                />

                <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                  Payment Released
                </Typography>

                <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                  The ${appConfig.membershipPrice} payment has been successfully released from all users.
                </Typography>

                <Button
                  variant="contained"
                  fullWidth
                  sx={{
                    bgcolor: '#2563EB',
                    color: 'white',
                    py: 1.5,
                    '&:hover': {
                      bgcolor: '#1D4ED8'
                    }
                  }}
                  onClick={handleSeeEventDetails}
                >
                  See Event Details
                </Button>
              </Paper>
            </Box>
          </CardContent>
        </Card>

        {/* Bottom Navigation */}
        <Paper
          elevation={3}
          sx={bottomNavStyle}
        >
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-around',
            py: 1,
            maxWidth: 500,
            mx: 'auto'
          }}>
            <Button
              color="inherit"
              sx={navButtonStyle}
            >
              <NotificationsIcon />
              <Typography variant="caption">Notification</Typography>
            </Button>

            <Button
              color="primary"
              sx={homeButtonStyle}
              onClick={() => navigate('/group-summary')}
            >
              <HomeIcon />
              <Typography variant="caption">Home</Typography>
            </Button>

            <Button
              color="inherit"
              sx={navButtonStyle}
            >
              <SettingsIcon />
              <Typography variant="caption">Settings</Typography>
            </Button>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default GroupFormationComplete;
