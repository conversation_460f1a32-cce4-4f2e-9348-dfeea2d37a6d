import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  IconButton,
  Card,
  CardContent,
  Paper,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
  Alert
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ShareIcon from '@mui/icons-material/Share';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import appConfig from '../../config/appConfig';
import {
  pageBackgroundStyle,
  mainCardStyle,
  headerWithBackStyle,
  bottomNavStyle,
  homeButtonStyle,
  navButtonStyle,
  contentPaperStyle,
  primaryButtonStyle,
  secondaryButtonStyle,
  outlinedButtonStyle,
  dialogPaperStyle
} from '../../styles/appStyles';

const GroupPreview = ({ user }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [groupMembers, setGroupMembers] = useState([]);
  const [matchCount, setMatchCount] = useState(0);

  // Fetch group data
  useEffect(() => {
    const fetchGroupData = async () => {
      try {
        // Get user data from localStorage
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        if (!userData || !userData.userId) {
          console.error('User data not found');
          return;
        }

        // Get all users from localStorage
        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');

        // Get all groups from localStorage
        const localGroups = JSON.parse(localStorage.getItem('localGroups') || '[]');

        // Find the user's group
        const userGroup = localGroups.find(group =>
          group.members && group.members.some(member => member.id === userData.userId)
        );

        let realMembers = [];

        if (userGroup) {
          // Get real members from the group (excluding current user)
          realMembers = userGroup.members
            .filter(member => member.id !== userData.userId)
            .map((member, index) => ({
              id: member.id,
              name: member.name,
              avatar: `/images/avatars/avatar${index + 1}.jpg`,
              isNew: index === userGroup.members.length - 2, // Second-to-last member is new
              location: member.city + ', ' + member.state,
              occupation: member.occupation || 'Professional',
              ethnicity: member.ethnicity || 'Not specified'
            }));
        }

        // If no real members exist, add one dummy member
        if (realMembers.length === 0) {
          realMembers = [
            {
              id: 'dummy-1',
              name: 'Aditi',
              avatar: '/images/avatars/avatar1.jpg',
              isNew: true,
              location: 'New York, NY',
              occupation: 'Software Engineer',
              ethnicity: 'Asian'
            }
          ];
        }

        setGroupMembers(realMembers);
        setMatchCount(realMembers.length + 1); // +1 for the current user
        setLoading(false);

        // If group is complete (4 members including current user), redirect to group summary
        if (realMembers.length + 1 === 4) {
          navigate('/group-summary');
        }
      } catch (error) {
        console.error('Error fetching group data:', error);
        // Fallback to mock data with one dummy member
        const mockMembers = [
          {
            id: 'dummy-1',
            name: 'Aditi',
            avatar: '/images/avatars/avatar1.jpg',
            isNew: true,
            location: 'New York, NY',
            occupation: 'Software Engineer',
            ethnicity: 'Asian'
          }
        ];
        setGroupMembers(mockMembers);
        setMatchCount(mockMembers.length + 1); // +1 for the current user
        setLoading(false);
      }
    };

    fetchGroupData();

    // Set up interval to periodically check for updates
    const intervalId = setInterval(fetchGroupData, 30000); // Check every 30 seconds

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [navigate]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle invite friends
  const handleInviteFriends = () => {
    // In a real app, this would open a share dialog
    alert('Invite friends functionality would be implemented here');
  };

  // States for dialogs and menus
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [withdrawDialogOpen, setWithdrawDialogOpen] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  // Generate a unique group link
  const groupLink = `https://mytribe.app/join/${Math.random().toString(36).substring(2, 10)}`;

  // Handle share button click
  const handleShareClick = () => {
    setShareDialogOpen(true);
  };

  // Handle copy link
  const handleCopyLink = () => {
    navigator.clipboard.writeText(groupLink)
      .then(() => {
        setSnackbarMessage('Link copied to clipboard!');
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
        setShareDialogOpen(false);
      })
      .catch(() => {
        setSnackbarMessage('Failed to copy link');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      });
  };

  // Handle menu open
  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle withdraw option
  const handleWithdrawOption = () => {
    handleMenuClose();
    setWithdrawDialogOpen(true);
  };

  // Handle withdraw confirmation
  const handleWithdrawConfirm = () => {
    // In a real app, this would call an API to process the withdrawal
    setWithdrawDialogOpen(false);
    navigate('/payment-success', { state: { withdrawn: true } });
  };

  // Handle withdraw cancel
  const handleWithdrawCancel = () => {
    setWithdrawDialogOpen(false);
  };

  return (
    <Box sx={pageBackgroundStyle}>
      <Box sx={{ position: 'relative', zIndex: 2, width: '100%', maxWidth: 500, mx: 'auto' }}>
        <Card sx={mainCardStyle}>
          <CardContent sx={{ p: 0 }}>
            {/* Header */}
            <Box sx={headerWithBackStyle}>
              <IconButton onClick={handleBack} edge="start" sx={{ color: 'white' }}>
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="h6" component="h1" sx={{ fontWeight: 500, color: 'white' }}>
                Finding Your Perfect Group
              </Typography>
              <IconButton edge="end" sx={{ color: 'white' }} onClick={handleShareClick}>
                <ShareIcon />
              </IconButton>
            </Box>

            {/* Progress Indicator */}
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
                <CircularProgress
                  variant="determinate"
                  value={(matchCount / appConfig.maxGroupSize) * 100}
                  size={80}
                  thickness={4}
                  sx={{ color: '#F5A623' }}
                />
                <Box
                  sx={{
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
                    {matchCount}/{appConfig.maxGroupSize}
                  </Typography>
                </Box>
              </Box>

              <Typography variant="h6" sx={{ mb: 1 }}>
                {matchCount === 1 ? 'Just getting started!' : 'Halfway there! More people are joining...'}
              </Typography>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Not sure? Tap for options.
              </Typography>

              {/* Group Preview */}
              <Paper
                elevation={0}
                sx={contentPaperStyle}
              >
                <Typography variant="subtitle1" sx={{ mb: 2, textAlign: 'left' }}>
                  Group Preview
                </Typography>

                <Box sx={{
                  position: 'relative',
                  height: 200,
                  border: '1px solid #F5A623',
                  borderRadius: 2,
                  bgcolor: 'rgba(245, 166, 35, 0.1)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>
                  {/* Table visualization */}
                  <Box sx={{
                    width: '70%',
                    height: '60%',
                    border: '2px solid #F5A623',
                    borderRadius: 4,
                    position: 'relative'
                  }}>
                    {/* Top position (You) */}
                    <Box sx={{
                      position: 'absolute',
                      top: '-30px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center'
                    }}>
                      <Box sx={{
                        border: '2px solid #F5A623',
                        borderRadius: 1,
                        px: 1,
                        py: 0.5,
                        bgcolor: 'rgba(245, 166, 35, 0.2)'
                      }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold', color: '#F5A623' }}>
                          You
                        </Typography>
                      </Box>
                    </Box>

                    {/* Right position (New member) */}
                    {groupMembers.length > 0 && (
                      <Box sx={{
                        position: 'absolute',
                        right: '-30px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center'
                      }}>
                        <Box sx={{
                          border: '2px solid #F5A623',
                          borderRadius: 1,
                          px: 1,
                          py: 0.5,
                          position: 'relative',
                          bgcolor: 'rgba(245, 166, 35, 0.2)',
                          overflow: 'hidden'
                        }}>
                          <Avatar
                            src={groupMembers[0].avatar}
                            sx={{
                              width: 24,
                              height: 24,
                              filter: 'blur(3px)',
                              transition: 'filter 0.3s ease',
                              '&:hover': { filter: 'blur(0)' }
                            }}
                          />
                          {groupMembers[0].isNew && (
                            <Box sx={{
                              position: 'absolute',
                              top: '-20px',
                              right: '-20px',
                              bgcolor: '#F5A623',
                              color: 'black',
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              fontSize: '0.7rem',
                              whiteSpace: 'nowrap'
                            }}>
                              {groupMembers[0].name} just joined!
                            </Box>
                          )}
                        </Box>
                      </Box>
                    )}

                    {/* Left position (Empty) */}
                    <Box sx={{
                      position: 'absolute',
                      left: '-30px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center'
                    }}>
                      <Box sx={{
                        border: '2px solid #F5A623',
                        borderRadius: 1,
                        width: 24,
                        height: 24,
                        bgcolor: 'rgba(245, 166, 35, 0.1)'
                      }} />
                    </Box>

                    {/* Bottom position (Empty) */}
                    <Box sx={{
                      position: 'absolute',
                      bottom: '-30px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center'
                    }}>
                      <Box sx={{
                        border: '2px solid #F5A623',
                        borderRadius: 1,
                        width: 24,
                        height: 24,
                        bgcolor: 'rgba(245, 166, 35, 0.1)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    sx={secondaryButtonStyle}
                    onClick={() => setInviteDialogOpen(true)}
                  >
                    Invite more friends to the table
                  </Button>

                  <Button
                    variant="outlined"
                    sx={{
                      ...outlinedButtonStyle,
                      minWidth: 'auto'
                    }}
                    onClick={handleMenuOpen}
                  >
                    <MoreVertIcon />
                  </Button>
                </Box>
              </Paper>
            </Box>
          </CardContent>
        </Card>

        {/* Bottom Navigation */}
        <Paper
          elevation={3}
          sx={bottomNavStyle}
        >
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-around',
            py: 1,
            maxWidth: 500,
            mx: 'auto'
          }}>
            <Button
              color="inherit"
              sx={navButtonStyle}
            >
              <NotificationsIcon />
              <Typography variant="caption">Notification</Typography>
            </Button>

            <Button
              color="primary"
              sx={homeButtonStyle}
            >
              <HomeIcon />
              <Typography variant="caption">Home</Typography>
            </Button>

            <Button
              color="inherit"
              sx={navButtonStyle}
            >
              <SettingsIcon />
              <Typography variant="caption">Settings</Typography>
            </Button>
          </Box>
        </Paper>
      </Box>

      {/* Share Dialog */}
      <Dialog
        open={shareDialogOpen}
        onClose={() => setShareDialogOpen(false)}
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
          Share Group Link
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Share this link with friends to invite them to your group:
          </Typography>
          <TextField
            fullWidth
            variant="outlined"
            value={groupLink}
            InputProps={{
              readOnly: true,
              endAdornment: (
                <IconButton edge="end" onClick={handleCopyLink} color="primary">
                  <ContentCopyIcon />
                </IconButton>
              ),
              sx: { color: 'white' }
            }}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)', px: 3, py: 2 }}>
          <Button
            onClick={() => setShareDialogOpen(false)}
            sx={outlinedButtonStyle}
            variant="outlined"
          >
            Cancel
          </Button>
          <Button
            onClick={handleCopyLink}
            variant="contained"
            sx={primaryButtonStyle}
          >
            Copy Link
          </Button>
        </DialogActions>
      </Dialog>

      {/* Invite Dialog */}
      <Dialog
        open={inviteDialogOpen}
        onClose={() => setInviteDialogOpen(false)}
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
          Invite Friends
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Share this link with friends to invite them to your group:
          </Typography>
          <TextField
            fullWidth
            variant="outlined"
            value={groupLink}
            InputProps={{
              readOnly: true,
              endAdornment: (
                <IconButton edge="end" onClick={handleCopyLink} color="primary">
                  <ContentCopyIcon />
                </IconButton>
              ),
              sx: { color: 'white' }
            }}
            sx={{ mb: 2 }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Your friends will be able to join your group by clicking this link.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)', px: 3, py: 2 }}>
          <Button
            onClick={() => setInviteDialogOpen(false)}
            sx={outlinedButtonStyle}
            variant="outlined"
          >
            Cancel
          </Button>
          <Button
            onClick={handleCopyLink}
            variant="contained"
            sx={primaryButtonStyle}
          >
            Copy Link
          </Button>
        </DialogActions>
      </Dialog>

      {/* Withdraw Dialog */}
      <Dialog
        open={withdrawDialogOpen}
        onClose={handleWithdrawCancel}
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
          Withdraw from Group?
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Are you sure you want to withdraw from this group? Your ${appConfig.membershipPrice} payment will be refunded.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Your spot will be made available to someone else. You can always join another group later.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)', px: 3, py: 2 }}>
          <Button
            onClick={handleWithdrawCancel}
            variant="contained"
            sx={secondaryButtonStyle}
          >
            Hold On
          </Button>
          <Button
            onClick={handleWithdrawConfirm}
            variant="outlined"
            color="error"
            sx={{ borderColor: 'rgba(255, 255, 255, 0.23)', color: 'white' }}
          >
            Withdraw Anyway
          </Button>
        </DialogActions>
      </Dialog>

      {/* Options Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            bgcolor: 'rgba(18, 18, 18, 0.95)',
            color: 'white',
            borderRadius: 2,
            minWidth: 180,
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
          }
        }}
      >
        <MenuItem onClick={handleWithdrawOption}>
          <ListItemIcon>
            <ExitToAppIcon sx={{ color: 'white' }} />
          </ListItemIcon>
          <ListItemText>Withdraw</ListItemText>
        </MenuItem>
      </Menu>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default GroupPreview;
