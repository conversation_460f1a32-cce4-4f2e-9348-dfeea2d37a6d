import React, { useState, useEffect, useContext } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import MatchService from '../../services/match.service';
import { ThemeContext } from '../../contexts/ThemeContext';
import appConfig from '../../config/appConfig';
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  AvatarGroup,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText
} from '@mui/material';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PersonIcon from '@mui/icons-material/Person';
import ImageGallery from '../common/ImageGallery';
import GroupsIcon from '@mui/icons-material/Groups';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ShareIcon from '@mui/icons-material/Share';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';

const GroupStatus = ({ user }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { mode } = useContext(ThemeContext);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [groupData, setGroupData] = useState(null);
  const [openExitDialog, setOpenExitDialog] = useState(false);
  const [exitLoading, setExitLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);



  // Fetch group data on component mount
  useEffect(() => {
    if (!user || !user.userId) {
      setError('User information is missing');
      setLoading(false);
      return;
    }

    const fetchGroupData = async () => {
      try {
        // Get group status from the backend
        const response = await MatchService.getGroupStatus(user.userId);
        const data = response.data;

        if (data.inGroup) {
          // Format the group data for display
          const formattedGroup = {
            id: data.groupId,
            isFinalized: data.finalized,
            members: data.members.map(member => ({
              id: member.id,
              name: member.name || `User-${member.id.substring(0, 5)}`,
              zip: member.zip || '',
              city: member.city || 'Unknown',
              state: member.state || '',
              gender: member.gender || '',
              interests: member.hobby ? [member.hobby] : []
            })),
            availableTimeSlots: data.members[0]?.availableTimeSlots || []
          };
          setGroupData(formattedGroup);
        } else {
          // User is not in a group
          setGroupData(null);
        }
      } catch (err) {
        console.error('Error fetching group data:', err);
        setError('Failed to fetch your group data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchGroupData();
  }, [user]);

  // Function to handle group exit
  const handleExitGroup = async () => {
    setExitLoading(true);

    try {
      if (!user || !user.userId) {
        throw new Error('User information is missing');
      }

      // Call the exit API
      try {
        const response = await MatchService.exitUser(user.userId);
        const data = response.data;

        if (data.success) {
          // Successfully exited the group
          setGroupData(null);
          setOpenExitDialog(false);
        } else {
          // Failed to exit group
          throw new Error(data.message || 'Failed to exit group');
        }
      } catch (apiError) {
        console.error('API error:', apiError);
        // Fallback for development/demo purposes
        // Remove user from local groups
        const localGroups = JSON.parse(localStorage.getItem('localGroups') || '[]');
        const updatedGroups = localGroups.map(group => {
          if (group.members.some(member => member.id === user.userId)) {
            // Remove user from this group
            return {
              ...group,
              members: group.members.filter(member => member.id !== user.userId)
            };
          }
          return group;
        }).filter(group => group.members.length > 0); // Remove empty groups

        localStorage.setItem('localGroups', JSON.stringify(updatedGroups));
        setGroupData(null);
        setOpenExitDialog(false);
      }
    } catch (err) {
      console.error('Error exiting group:', err);
      setError(err.message || 'Failed to exit group. Please try again.');
    } finally {
      setExitLoading(false);
      setOpenExitDialog(false);
    }
  };

  // Function to handle refreshing group status
  const handleRefresh = async () => {
    setRefreshing(true);
    setError('');

    try {
      if (!user || !user.userId) {
        throw new Error('User information is missing');
      }

      // Get updated group status from the backend
      const response = await MatchService.getGroupStatus(user.userId);
      const data = response.data;

      if (data.inGroup) {
        // Format the group data for display
        const formattedGroup = {
          id: data.groupId,
          isFinalized: data.finalized,
          members: data.members.map(member => ({
            id: member.id,
            name: member.name || `User-${member.id.substring(0, 5)}`,
            zip: member.zip || '',
            city: member.city || 'Unknown',
            state: member.state || '',
            gender: member.gender || '',
            interests: member.hobby ? [member.hobby] : []
          })),
          availableTimeSlots: data.members[0]?.availableTimeSlots || []
        };
        setGroupData(formattedGroup);
      } else {
        // User is not in a group
        setGroupData(null);
      }
    } catch (err) {
      console.error('Error refreshing group data:', err);
      setError('Failed to refresh group data. Please try again.');
    } finally {
      setRefreshing(false);
    }
  };

  // Function to handle finding a group
  const handleFindGroup = async () => {
    setLoading(true);
    setError('');

    try {
      // Get current user profile data
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const profile = currentUser.profile;

      if (!profile) {
        setError('Please complete your profile before finding a tribe');
        setLoading(false);
        return;
      }

      // Prepare the match request data
      const matchData = {
        zip: profile.zipCode,
        gender: profile.gender,
        interest: profile.interests && profile.interests.length > 0 ? profile.interests[0] : 'general',
        availableTimeSlots: profile.availableTimeSlots || []
      };

      // Call the match API
      const response = await MatchService.matchUser(matchData);
      const data = response.data;

      if (data.matched) {
        // User was matched to a group
        const formattedGroup = {
          id: data.groupId,
          isFinalized: data.finalized,
          members: data.members.map(member => ({
            id: member.id,
            name: member.name || `User-${member.id.substring(0, 5)}`,
            zip: member.zip || '',
            city: member.city || 'Unknown',
            state: member.state || '',
            gender: member.gender || '',
            interests: member.hobby ? [member.hobby] : []
          })),
          availableTimeSlots: data.members[0]?.availableTimeSlots || []
        };
        setGroupData(formattedGroup);
      } else {
        // User was added to waiting pool
        setError(data.message || 'You have been added to the waiting pool. Check back later!');
      }
    } catch (err) {
      console.error('Error finding group:', err);
      setError('Failed to find a group. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to format time slot for display
  const formatTimeSlot = (slot) => {
    switch (slot) {
      case 'THURSDAY_7PM':
        return 'Thursday 7:00–9:00pm';
      case 'FRIDAY_7PM':
        return 'Friday 7:00–9:00pm';
      case 'SATURDAY_BRUNCH':
        return 'Saturday 11:00am–1:00pm';
      default:
        return slot;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading your tribe information...
        </Typography>
      </Container>
    );
  }

  // Render error state
  if (error) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </Container>
    );
  }

  // Render when user is not in a group
  if (!groupData) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
          <HourglassEmptyIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            You're not in a tribe yet
          </Typography>
          <Typography variant="body1" paragraph>
            Join a tribe to connect with like-minded people in your area.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleFindGroup}
            sx={{ mt: 2 }}
          >
            Find My Tribe
          </Button>
        </Paper>
      </Container>
    );
  }

  // Render when user is in a group
  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        {/* Group Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <GroupsIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
          <Typography variant="h4">
            Your Tribe
          </Typography>
          <Chip
            label={groupData.isFinalized ? "Complete" : `${groupData.members.length}/4 Members`}
            color={groupData.isFinalized ? "success" : "primary"}
            sx={{ ml: 'auto' }}
          />
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Group Status */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Status
          </Typography>
          <Card variant="outlined">
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Group ID
                  </Typography>
                  <Typography variant="body1">
                    {groupData.id}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Status
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {groupData.isFinalized ? (
                      <>
                        <CheckCircleIcon sx={{ color: 'success.main', mr: 1 }} />
                        <Typography variant="body1" color="success.main">
                          Tribe Complete
                        </Typography>
                      </>
                    ) : groupData.members.length === 3 ? (
                      <>
                        <HourglassEmptyIcon sx={{ color: 'warning.main', mr: 1 }} />
                        <Typography variant="body1" color="warning.main">
                          Waiting for 1 more member
                        </Typography>
                      </>
                    ) : (
                      <>
                        <HourglassEmptyIcon sx={{ color: 'primary.main', mr: 1 }} />
                        <Typography variant="body1">
                          Waiting for {4 - groupData.members.length} more members
                        </Typography>
                      </>
                    )}
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Available Time Slots
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {groupData.availableTimeSlots.map((slot) => (
                      <Chip
                        key={slot}
                        label={formatTimeSlot(slot)}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>

        {/* Group Members */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              '&::after': {
                content: '""',
                display: 'block',
                height: 2,
                flexGrow: 1,
                backgroundColor: alpha(theme.palette.primary.main, 0.2),
                ml: 2,
                borderRadius: 1
              }
            }}
          >
            Current Tribe Members ({groupData.members.length}/4)
          </Typography>
          <List>
            {groupData.members.map((member) => (
              <ListItem
                key={member.id}
                alignItems="flex-start"
                sx={{
                  bgcolor: member.id === user?.id ?
                    alpha(theme.palette.primary.main, 0.08) :
                    mode === 'dark' ? alpha(theme.palette.background.paper, 0.3) : 'transparent',
                  borderRadius: 2,
                  mb: 1,
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  boxShadow: member.id === user?.id ?
                    `0 4px 8px ${alpha(theme.palette.primary.main, 0.15)}` :
                    'none',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    bgcolor: member.id === user?.id ?
                      alpha(theme.palette.primary.main, 0.12) :
                      alpha(theme.palette.background.paper, 0.5),
                    boxShadow: `0 4px 12px ${alpha(theme.palette.divider, 0.2)}`
                  }
                }}
              >
                <ListItemAvatar>
                  <Avatar>
                    <PersonIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="subtitle1">
                        {member.name}
                      </Typography>
                      {member.id === user?.id && (
                        <Chip
                          label="You"
                          size="small"
                          color="primary"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                        <LocationOnIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                        <Typography
                          component="span"
                          variant="body2"
                          color="text.secondary"
                        >
                          {member.city}, {member.state} ({member.zip})
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {member.interests.map((interest) => (
                          <Chip
                            key={interest}
                            label={interest}
                            size="small"
                            variant="outlined"
                            sx={{ height: 24 }}
                          />
                        ))}
                      </Box>
                    </>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Box>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          {!groupData.isFinalized && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<ExitToAppIcon />}
              onClick={() => setOpenExitDialog(true)}
            >
              Exit Tribe
            </Button>
          )}

          {groupData.isFinalized && (
            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              Your tribe is now complete! You cannot leave this tribe.
            </Typography>
          )}

          <Button
            variant="contained"
            color="primary"
            onClick={handleRefresh}
            disabled={refreshing}
            startIcon={refreshing ? <CircularProgress size={20} color="inherit" /> : null}
            sx={{
              py: 1.2,
              px: 3,
              borderRadius: 2,
              fontWeight: 600,
              boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
              '&:hover': {
                boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.4)}`,
                transform: 'translateY(-2px)'
              },
              transition: 'all 0.2s ease'
            }}
          >
            {refreshing ? 'Refreshing...' : 'Refresh Status'}
          </Button>
        </Box>
      </Paper>

      {/* Image Gallery */}
      {!groupData && (
        <Box sx={{ mt: 6, mb: 4 }}>
          <ImageGallery variant="group" />
        </Box>
      )}

      {/* Exit Confirmation Dialog */}
      <Dialog
        open={openExitDialog}
        onClose={() => setOpenExitDialog(false)}
      >
        <DialogTitle>
          Exit Tribe?
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to exit this tribe? You'll be placed back in the waiting pool for matching.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenExitDialog(false)} disabled={exitLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleExitGroup}
            color="error"
            disabled={exitLoading}
            startIcon={exitLoading ? <CircularProgress size={20} /> : null}
          >
            {exitLoading ? 'Exiting...' : 'Exit Tribe'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default GroupStatus;
