import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import appConfig from '../../config/appConfig';
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Avatar,
  AvatarGroup,
  CircularProgress,
  IconButton,
  Card,
  CardContent
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ShareIcon from '@mui/icons-material/Share';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';

const GroupStatusNew = ({ user }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [groupMembers, setGroupMembers] = useState([]);
  const [matchCount, setMatchCount] = useState(0);

  // Mock data for group members
  useEffect(() => {
    // In a real app, this would be an API call to get group members
    setTimeout(() => {
      // For testing, you can change this number to simulate different group sizes
      const groupSize = 3; // Change to 3 to simulate a group of 4 (including current user)

      const mockMembers = [];
      for (let i = 1; i <= groupSize; i++) {
        mockMembers.push({
          id: i,
          name: ['Alex', 'Aditi', 'Carlos'][i-1],
          avatar: `/images/avatars/avatar${i}.jpg`,
          isNew: i === groupSize // Only the newest member is marked as new
        });
      }

      setGroupMembers(mockMembers);
      setMatchCount(mockMembers.length + 1); // +1 for the current user
      setLoading(false);

      // If group is complete (4 members including current user), redirect to group summary
      if (mockMembers.length + 1 === 4) {
        navigate('/group-summary');
      }
    }, 1500);
  }, [navigate]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle invite friends
  const handleInviteFriends = () => {
    // In a real app, this would open a share dialog
    alert('Invite friends functionality would be implemented here');
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundImage: 'url(/images/beach-fun.jpg)',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        zIndex: 1
      }
    }}>
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 2, py: 2 }}>
        <Card sx={{
          borderRadius: 4,
          overflow: 'hidden',
          boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
          bgcolor: 'rgba(18, 18, 18, 0.8)',
          color: 'white',
          mb: 2
        }}>
          <CardContent sx={{ p: 0 }}>
            {/* Header */}
            <Box sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
            }}>
              <IconButton onClick={handleBack} edge="start" sx={{ color: 'white' }}>
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="h6" component="h1" sx={{ fontWeight: 500, color: 'white' }}>
                Finding Your Perfect Group
              </Typography>
              <IconButton edge="end" sx={{ color: 'white' }}>
                <ShareIcon />
              </IconButton>
            </Box>

            {/* Progress Indicator */}
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
                <CircularProgress
                  variant="determinate"
                  value={(matchCount / appConfig.maxGroupSize) * 100}
                  size={80}
                  thickness={4}
                  sx={{ color: '#F5A623' }}
                />
                <Box
                  sx={{
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
                    {matchCount}/{appConfig.maxGroupSize}
                  </Typography>
                </Box>
              </Box>

              <Typography variant="h6" sx={{ mb: 1 }}>
                {matchCount === 1 ? 'Just getting started!' : 'Halfway there! More people are joining...'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Not sure? Tap for options.
              </Typography>
            </Box>

            {/* Group Preview */}
            <Box sx={{ px: 3, pb: 3 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  borderRadius: 3,
                  bgcolor: 'white',
                  color: 'text.primary',
                  mb: 3
                }}
              >
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                  Group Preview
                </Typography>

                <Box sx={{
                  position: 'relative',
                  height: 200,
                  border: '1px solid rgba(0,0,0,0.1)',
                  borderRadius: 2,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  mb: 3
                }}>
                  {/* Table visualization */}
                  <Box sx={{
                    width: '80%',
                    height: '60%',
                    border: '2px solid #000',
                    borderRadius: 2,
                    position: 'relative'
                  }}>
                    {/* Top position (You) */}
                    <Box sx={{
                      position: 'absolute',
                      top: '-30px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center'
                    }}>
                      <Box sx={{
                        border: '2px solid #000',
                        borderRadius: 1,
                        px: 1,
                        py: 0.5,
                        bgcolor: 'white'
                      }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                          You
                        </Typography>
                      </Box>
                    </Box>

                    {/* Right position (New member) */}
                    {groupMembers.length > 0 && (
                      <Box sx={{
                        position: 'absolute',
                        right: '-30px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center'
                      }}>
                        <Box sx={{
                          border: '2px solid #000',
                          borderRadius: 1,
                          px: 1,
                          py: 0.5,
                          position: 'relative'
                        }}>
                          <Avatar
                            src={groupMembers[0].avatar}
                            sx={{ width: 24, height: 24 }}
                          />
                          {groupMembers[0].isNew && (
                            <Box sx={{
                              position: 'absolute',
                              top: '-20px',
                              right: '-20px',
                              bgcolor: '#F5A623',
                              color: 'white',
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              fontSize: '0.7rem',
                              whiteSpace: 'nowrap'
                            }}>
                              Aditi just joined!
                            </Box>
                          )}
                        </Box>
                      </Box>
                    )}

                    {/* Left position (Empty) */}
                    <Box sx={{
                      position: 'absolute',
                      left: '-30px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center'
                    }}>
                      <Box sx={{
                        border: '2px solid #000',
                        borderRadius: 1,
                        width: 30,
                        height: 30
                      }} />
                    </Box>

                    {/* Bottom position (Empty) */}
                    <Box sx={{
                      position: 'absolute',
                      bottom: '-30px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center'
                    }}>
                      <Box sx={{
                        border: '2px solid #000',
                        borderRadius: 1,
                        width: 30,
                        height: 15
                      }} />
                    </Box>
                  </Box>
                </Box>

                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={handleInviteFriends}
                  sx={{
                    borderRadius: 8,
                    py: 1.5,
                    mb: 1
                  }}
                >
                  Invite more friends to the table
                </Button>

                <Button
                  variant="outlined"
                  color="inherit"
                  fullWidth
                  sx={{
                    borderRadius: 8,
                    py: 1.5,
                    minWidth: 'auto',
                    maxWidth: 50,
                    ml: 'auto'
                  }}
                >
                  <MoreVertIcon />
                </Button>
              </Paper>
            </Box>
          </CardContent>
        </Card>

        {/* Bottom Navigation */}
        <Paper
          elevation={3}
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 10,
            bgcolor: '#0A2647',
            borderTop: '1px solid rgba(255,255,255,0.1)'
          }}
        >
          <Container maxWidth="sm">
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-around',
              py: 1
            }}>
              <Button
                color="inherit"
                sx={{
                  flexDirection: 'column',
                  color: 'rgba(255,255,255,0.7)',
                  minWidth: 'auto'
                }}
              >
                <NotificationsIcon />
                <Typography variant="caption">Notification</Typography>
              </Button>

              <Button
                color="primary"
                sx={{
                  flexDirection: 'column',
                  bgcolor: 'rgba(37, 99, 235, 0.2)',
                  borderRadius: '50%',
                  p: 1,
                  minWidth: 'auto',
                  '&:hover': {
                    bgcolor: 'rgba(37, 99, 235, 0.3)',
                  }
                }}
              >
                <HomeIcon />
                <Typography variant="caption">Home</Typography>
              </Button>

              <Button
                color="inherit"
                sx={{
                  flexDirection: 'column',
                  color: 'rgba(255,255,255,0.7)',
                  minWidth: 'auto'
                }}
              >
                <SettingsIcon />
                <Typography variant="caption">Settings</Typography>
              </Button>
            </Box>
          </Container>
        </Paper>
      </Container>
    </Box>
  );
};

export default GroupStatusNew;
