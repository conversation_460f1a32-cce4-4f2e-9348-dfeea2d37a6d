import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Container,
  Paper,
  Avatar,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Card,
  CardContent,
  Divider,
  Collapse,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CloseIcon from '@mui/icons-material/Close';
import DirectionsIcon from '@mui/icons-material/Directions';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import WorkIcon from '@mui/icons-material/Work';
import PersonIcon from '@mui/icons-material/Person';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import {
  pageBackgroundStyle,
  mainCardStyle,
  headerWithBackStyle,
  bottomNavStyle,
  homeButtonStyle,
  navButtonStyle,
  primaryButtonStyle,
  secondaryButtonStyle,
  contentPaperStyle,
  dialogPaperStyle
} from '../../styles/appStyles';

const GroupSummary = ({ user }) => {
  const navigate = useNavigate();
  const [groupData, setGroupData] = useState(null);
  const [insightsDialogOpen, setInsightsDialogOpen] = useState(false);
  const [viewDetailsOpen, setViewDetailsOpen] = useState(false);
  const [membersExpanded, setMembersExpanded] = useState(false);
  const [checkedIn, setCheckedIn] = useState(false);
  const [checkedOut, setCheckedOut] = useState(false);

  // Fetch group data
  useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    const mockGroupData = {
      id: 'group-123',
      name: 'Weekend Meetup Club',
      venue: 'Central Park Cafe',
      address: '123 Main St, Springfield',
      date: 'March 25, 2025',
      time: '3:00 PM',
      facts: [
        'Lorem ipsum dolor sit amet.',
        'Adipiscing elit. Sed do eiusmod tem.'
      ],
      members: [
        {
          id: user?.userId || 'user-1',
          name: 'You',
          avatar: '/images/avatars/avatar1.jpg',
          occupation: 'Software Engineer, Writer',
          ethnicity: 'Middle Eastern'
        },
        {
          id: 'user-2',
          name: 'Aditi',
          avatar: '/images/avatars/avatar2.jpg',
          occupation: 'Chef, Gamer',
          ethnicity: 'European'
        },
        {
          id: 'user-3',
          name: 'Alex',
          avatar: '/images/avatars/avatar3.jpg',
          occupation: 'Music Producer, Travel Blogger',
          ethnicity: 'Indigenous'
        },
        {
          id: 'user-4',
          name: 'Carlos',
          avatar: '/images/avatars/avatar4.jpg',
          occupation: 'Photography, Art',
          ethnicity: 'Caribbean'
        }
      ],
      insights: [
        {
          title: 'Loves Hiking',
          description: 'Has hiked in several countries and enjoys challenging trails.',
          image: '/images/insights/hiking.svg'
        },
        {
          title: 'Speaks Three Languages',
          description: 'Fluent in Spanish, French, and English.',
          image: '/images/insights/languages.svg'
        },
        {
          title: 'Coffee Enthusiast',
          description: 'Knows everything about coffee brewing methods and coffee varieties.',
          image: '/images/insights/coffee.svg'
        },
        {
          title: 'Book Lover',
          description: 'Has read "The 3 Mistakes of My Life"',
          image: '/images/insights/books.svg'
        }
      ]
    };

    setGroupData(mockGroupData);
  }, [user]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle view details
  const handleViewDetails = () => {
    setViewDetailsOpen(true);
  };

  // Handle insights dialog
  const handleInsightsOpen = () => {
    setInsightsDialogOpen(true);
  };

  // Handle directions
  const handleDirections = () => {
    // Open maps with directions to the exact venue address
    const exactAddress = `${groupData.venue}, ${groupData.address}`;
    window.open(`https://maps.google.com/?q=${encodeURIComponent(exactAddress)}`, '_blank');
  };

  // Handle games and ice breakers button click
  const handleGamesClick = () => {
    navigate('/games-ice-breakers');
  };

  // Toggle members expanded state
  const toggleMembersExpanded = () => {
    setMembersExpanded(!membersExpanded);
  };

  // Handle check-in
  const handleCheckIn = () => {
    setCheckedIn(true);
    setCheckedOut(false);
    alert('You have checked in to the event!');
  };

  // Handle check-out
  const handleCheckOut = () => {
    setCheckedOut(true);
    setCheckedIn(false);
    alert('You have checked out from the event!');
  };

  if (!groupData) {
    return (
      <Box sx={pageBackgroundStyle}>
        <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 2, py: 4 }}>
          <Typography variant="h5" color="white" align="center">
            Loading group data...
          </Typography>
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={pageBackgroundStyle}>
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 2, py: 2 }}>
        <Paper
          elevation={3}
          sx={{
            bgcolor: 'rgba(18, 18, 18, 0.8)',
            color: 'white',
            borderRadius: 4,
            overflow: 'hidden',
            mb: 2
          }}
        >
          {/* Header */}
          <Box sx={headerWithBackStyle}>
            <IconButton
              onClick={handleBack}
              sx={{ color: 'white', mr: 1 }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6">
              Group and Event Summary
            </Typography>
          </Box>

          {/* Group Preview Section */}
          <Box sx={{ p: 2, bgcolor: 'rgba(255,255,255,0.05)' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Group Preview
              </Typography>
              <Button
                variant="text"
                color="primary"
                onClick={toggleMembersExpanded}
                sx={{ color: '#F5A623' }}
                endIcon={membersExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              >
                {membersExpanded ? 'Hide Details' : 'View Details'}
              </Button>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              {groupData.members.map((member) => (
                <Box
                  key={member.id}
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    width: '25%'
                  }}
                >
                  <Avatar
                    src={member.avatar}
                    alt={member.name}
                    sx={{
                      width: 56,
                      height: 56,
                      border: '2px solid white',
                      mb: 1
                    }}
                  />
                  <Typography variant="body2" align="center">
                    {member.name}
                  </Typography>
                </Box>
              ))}
            </Box>

            {/* Collapsible Member Details */}
            <Collapse in={membersExpanded} timeout="auto" unmountOnExit>
              <List sx={{ bgcolor: 'rgba(0,0,0,0.2)', borderRadius: 1, mt: 2 }}>
                {groupData.members.map((member) => (
                  <ListItem key={member.id} sx={{ borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
                    <ListItemAvatar>
                      <Avatar src={member.avatar} alt={member.name} />
                    </ListItemAvatar>
                    <ListItemText
                      primary={member.name}
                      secondary={
                        <React.Fragment>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <WorkIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.8rem', color: 'rgba(255,255,255,0.6)' }} />
                            <Typography variant="body2" color="text.secondary" component="span">
                              {member.occupation}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <LocationOnIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.8rem', color: 'rgba(255,255,255,0.6)' }} />
                            <Typography variant="body2" color="text.secondary" component="span">
                              {member.location || 'New York, NY'}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <PersonIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.8rem', color: 'rgba(255,255,255,0.6)' }} />
                            <Typography variant="body2" color="text.secondary" component="span">
                              {member.ethnicity}
                            </Typography>
                          </Box>
                        </React.Fragment>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Collapse>
          </Box>

          {/* Group Details */}
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Group : {groupData.name}
            </Typography>
            <Typography variant="subtitle1" gutterBottom>
              Venue : {groupData.venue}
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, color: 'rgba(255,255,255,0.7)' }}>
              <LocationOnIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography variant="body2">
                {groupData.address}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, color: 'rgba(255,255,255,0.7)' }}>
              <CalendarTodayIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography variant="body2">
                {groupData.date}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, mb: 2, color: 'rgba(255,255,255,0.7)' }}>
              <AccessTimeIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography variant="body2">
                {groupData.time}
              </Typography>
            </Box>

            <Typography variant="h6" gutterBottom>
              Great fact about {groupData.venue}
            </Typography>

            {groupData.facts.map((fact, index) => (
              <Typography key={index} variant="body2" sx={{ mb: 1 }}>
                Fact : {index + 1} {fact}
              </Typography>
            ))}

            <Button
              variant="contained"
              fullWidth
              startIcon={<DirectionsIcon />}
              onClick={handleDirections}
              sx={{ ...primaryButtonStyle, mt: 2 }}
            >
              Direction
            </Button>
          </Box>

          {/* Check-in/Check-out Buttons */}
          <Box sx={{ p: 2, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              fullWidth
              onClick={handleCheckIn}
              disabled={checkedIn}
              sx={{
                bgcolor: checkedIn ? '#388e3c' : '#4caf50',
                color: 'white',
                '&:hover': { bgcolor: '#388e3c' },
                '&.Mui-disabled': {
                  bgcolor: '#388e3c',
                  color: 'white',
                  opacity: 0.8
                }
              }}
            >
              {checkedIn ? 'Checked In ✓' : 'Check In'}
            </Button>

            <Button
              variant="contained"
              fullWidth
              onClick={handleCheckOut}
              disabled={checkedOut}
              sx={{
                bgcolor: checkedOut ? '#d32f2f' : '#f44336',
                color: 'white',
                '&:hover': { bgcolor: '#d32f2f' },
                '&.Mui-disabled': {
                  bgcolor: '#d32f2f',
                  color: 'white',
                  opacity: 0.8
                }
              }}
            >
              {checkedOut ? 'Checked Out ✓' : 'Check Out'}
            </Button>
          </Box>

          {/* Action Buttons */}
          <Box sx={{ p: 2, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              fullWidth
              onClick={handleInsightsOpen}
              sx={{ bgcolor: '#3f51b5', color: 'white', '&:hover': { bgcolor: '#303f9f' } }}
            >
              Anonymous Group Insights
            </Button>

            <Button
              variant="contained"
              fullWidth
              onClick={handleGamesClick}
              startIcon={<SportsEsportsIcon />}
              sx={{ bgcolor: '#F5A623', color: 'black', '&:hover': { bgcolor: '#e69c1f' } }}
            >
              Games & Ice Breakers
            </Button>
          </Box>

          {/* Second Location */}
          <Box sx={{ p: 2, textAlign: 'center', mt: 2 }}>
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                bgcolor: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto',
                mb: 2
              }}
            >
              <LocationOnIcon sx={{ color: '#3f51b5' }} />
            </Box>
            <Typography variant="h6" sx={{ color: '#F5A623' }}>
              Second Location
            </Typography>
          </Box>
        </Paper>

        {/* Bottom Navigation */}
        <Paper
          elevation={3}
          sx={bottomNavStyle}
        >
          <Container maxWidth="sm">
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-around',
              py: 1
            }}>
              <Button
                color="inherit"
                sx={navButtonStyle}
              >
                <NotificationsIcon />
                <Typography variant="caption">Notification</Typography>
              </Button>

              <Button
                color="primary"
                sx={homeButtonStyle}
              >
                <HomeIcon />
                <Typography variant="caption">Home</Typography>
              </Button>

              <Button
                color="inherit"
                sx={navButtonStyle}
              >
                <SettingsIcon />
                <Typography variant="caption">Settings</Typography>
              </Button>
            </Box>
          </Container>
        </Paper>
      </Container>

      {/* View Details Dialog - Keeping this for backward compatibility */}
      <Dialog
        open={viewDetailsOpen}
        onClose={() => setViewDetailsOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid rgba(255,255,255,0.1)'
        }}>
          <Typography variant="h6">Group Details</Typography>
          <IconButton
            onClick={() => setViewDetailsOpen(false)}
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Group : {groupData.name}
          </Typography>
          <Typography variant="subtitle1" gutterBottom>
            Venue : {groupData.venue}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, color: 'rgba(255,255,255,0.7)' }}>
            <LocationOnIcon fontSize="small" sx={{ mr: 1 }} />
            <Typography variant="body2">
              {groupData.address}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, color: 'rgba(255,255,255,0.7)' }}>
            <CalendarTodayIcon fontSize="small" sx={{ mr: 1 }} />
            <Typography variant="body2">
              {groupData.date}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, mb: 2, color: 'rgba(255,255,255,0.7)' }}>
            <AccessTimeIcon fontSize="small" sx={{ mr: 1 }} />
            <Typography variant="body2">
              {groupData.time}
            </Typography>
          </Box>

          <Typography variant="h6" gutterBottom>
            Great fact about {groupData.venue}
          </Typography>

          {groupData.facts.map((fact, index) => (
            <Typography key={index} variant="body2" sx={{ mb: 1 }}>
              Fact : {index + 1} {fact}
            </Typography>
          ))}
        </DialogContent>
      </Dialog>

      {/* Insights Dialog */}
      <Dialog
        open={insightsDialogOpen}
        onClose={() => setInsightsDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid rgba(255,255,255,0.1)'
        }}>
          <Typography variant="h6">Anonymous Group Insights</Typography>
          <IconButton
            onClick={() => setInsightsDialogOpen(false)}
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 2 }}>
          <Grid container spacing={2}>
            {groupData.insights.map((insight, index) => (
              <Grid item xs={6} key={index}>
                <Card sx={{
                  bgcolor: 'white',
                  color: 'black',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {insight.title}
                    </Typography>
                    <Box
                      component="img"
                      src={insight.image}
                      alt={insight.title}
                      sx={{
                        width: '100%',
                        height: 120,
                        objectFit: 'contain',
                        mb: 2
                      }}
                    />
                    <Typography variant="body2">
                      {insight.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default GroupSummary;
