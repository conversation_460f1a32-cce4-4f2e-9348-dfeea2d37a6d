import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Paper,
  Avatar,
  AvatarGroup,
  Container,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SecurityIcon from '@mui/icons-material/Security';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import appConfig from '../../config/appConfig';
import {
  pageBackgroundStyle,
  mainCardStyle,
  cardHeaderStyle,
  bottomNavStyle,
  homeButtonStyle,
  navButtonStyle,
  primaryButtonStyle,
  secondaryButtonStyle,
  outlinedButtonStyle,
  dialogPaperStyle
} from '../../styles/appStyles';

const PaymentSuccess = ({ user }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [groupMembers, setGroupMembers] = useState([]);
  const [withdrawalDialogOpen, setWithdrawalDialogOpen] = useState(false);

  // Check if user has withdrawn
  const isWithdrawn = location.state?.withdrawn === true;

  // Fetch group members data
  useEffect(() => {
    const fetchGroupData = async () => {
      try {
        // Get user data from localStorage
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        if (!userData || !userData.userId) {
          console.error('User data not found');
          return;
        }

        // In a real app, this would be an API call to get group members
        // For now, we'll check if there's any group data in localStorage
        const localGroups = JSON.parse(localStorage.getItem('localGroups') || '[]');
        const userGroup = localGroups.find(group =>
          group.members && group.members.some(member => member.id === userData.userId)
        );

        if (userGroup && userGroup.members) {
          // Filter out the current user
          const otherMembers = userGroup.members.filter(member => member.id !== userData.userId);

          // Format members for display
          const formattedMembers = otherMembers.map(member => ({
            id: member.id,
            name: member.name || `User-${member.id.substring(0, 5)}`,
            avatar: member.avatar || `/images/avatars/avatar${Math.floor(Math.random() * 5) + 1}.jpg`,
            isNew: true // For demo purposes, mark all as new
          }));

          setGroupMembers(formattedMembers);
        } else {
          // If no group found, use mock data for demo
          const mockMembers = [
            { id: 1, name: 'Alex', avatar: '/images/avatars/avatar1.jpg', isNew: true }
          ];
          setGroupMembers(mockMembers);
        }
      } catch (error) {
        console.error('Error fetching group data:', error);
        // Fallback to mock data
        const mockMembers = [
          { id: 1, name: 'Alex', avatar: '/images/avatars/avatar1.jpg', isNew: true }
        ];
        setGroupMembers(mockMembers);
      }
    };

    fetchGroupData();

    // Set up interval to periodically check for updates
    const intervalId = setInterval(fetchGroupData, 30000); // Check every 30 seconds

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [navigate]);

  // Handle check group status
  const handleCheckGroupStatus = () => {
    // Check if user is in a group
    const localGroups = JSON.parse(localStorage.getItem('localGroups') || '[]');
    const userGroup = localGroups.find(group =>
      group.members && group.members.some(member => member.id === user?.userId)
    );

    if (userGroup) {
      // If group has 4 members (complete), go to group summary
      if (userGroup.members.length === 4) {
        navigate('/group-summary');
      } else {
        // Otherwise go to group status
        navigate('/group-status');
      }
    } else {
      // User has no group yet, go to group preview
      navigate('/group-preview');
    }
  };

  // Handle withdraw
  const handleWithdraw = () => {
    // In a real app, this would be an API call to process refund
    alert('Refund process would be initiated here');
  };

  // Handle withdrawal confirmation
  const handleWithdrawalConfirm = () => {
    setWithdrawalDialogOpen(false);
    // In a real app, this would call an API to process the withdrawal
    // Navigate to home page after withdrawal
    navigate('/');
  };

  // Handle withdrawal cancellation
  const handleWithdrawalCancel = () => {
    setWithdrawalDialogOpen(false);
  };

  return (
    <Box sx={pageBackgroundStyle}>
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 2 }}>
        <Card sx={mainCardStyle}>
          <CardContent sx={{ p: 0 }}>
            {/* Header */}
            <Box sx={cardHeaderStyle}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Chip
                  label={(() => {
                    // Get user location from profile if available
                    const userData = JSON.parse(localStorage.getItem('user') || '{}');
                    if (userData.profile) {
                      if (userData.profile.city && userData.profile.state) {
                        return `${userData.profile.city}, ${userData.profile.state}`;
                      } else if (userData.profile.city) {
                        return userData.profile.city;
                      } else if (userData.profile.state) {
                        return userData.profile.state;
                      } else if (userData.profile.zipCode) {
                        return `Zip: ${userData.profile.zipCode}`;
                      }
                    }
                    return 'USA';
                  })()}
                  variant="outlined"
                  size="small"
                  sx={{
                    color: 'white',
                    borderColor: 'rgba(255,255,255,0.3)',
                    mr: 1
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
                    Your Spot is Secured!
                  </Typography>

                  <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.7)', mb: 1 }}>
                    Your payment is 100% safe & refundable!
                  </Typography>

                  <Button
                    variant="text"
                    color="warning"
                    onClick={handleCheckGroupStatus}
                    endIcon={<ArrowForwardIcon />}
                    sx={{ textTransform: 'none', p: 0, fontWeight: 600 }}
                  >
                    Check Group Status
                  </Button>
                </Box>

                <Box sx={{ position: 'relative' }}>
                  <SecurityIcon sx={{ fontSize: 60, color: '#F5A623', opacity: 0.9 }} />
                  <CheckCircleIcon sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    color: 'white',
                    fontSize: 24
                  }} />
                </Box>
              </Box>
            </Box>

            {/* Group Status */}
            <Box sx={{ p: 3 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  borderRadius: 3,
                  bgcolor: 'white',
                  color: 'text.primary',
                  mb: 3
                }}
              >
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Group Status:
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <AvatarGroup max={4} sx={{ mb: 2 }}>
                    <Avatar alt="You" src="/static/images/avatar/1.jpg" sx={{ width: 56, height: 56 }} />
                    {groupMembers.map(member => (
                      <Avatar
                        key={member.id}
                        alt={member.name}
                        src={member.avatar}
                        sx={{ width: 56, height: 56 }}
                      />
                    ))}
                    {[...Array(Math.max(0, appConfig.maxGroupSize - groupMembers.length - 1))].map((_, i) => (
                      <Avatar key={i} sx={{ width: 56, height: 56, bgcolor: 'rgba(0,0,0,0.1)' }} />
                    ))}
                  </AvatarGroup>

                  <Typography variant="body1" color="primary" sx={{ fontWeight: 500, mb: 1 }}>
                    {groupMembers.length > 0 ? `${groupMembers[groupMembers.length-1].name} just joined your group!` : 'Waiting for others to join'}
                  </Typography>

                  <Typography variant="body1" sx={{ fontWeight: 600, color: '#F5A623' }}>
                    Great! Now need {appConfig.maxGroupSize - groupMembers.length - 1} more to start
                  </Typography>
                </Box>
              </Paper>

              {/* Refund Info */}
              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  borderRadius: 3,
                  bgcolor: 'rgba(255,255,255,0.05)',
                  mb: 3,
                  border: '1px solid rgba(255,255,255,0.1)'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{
                    mr: 2,
                    bgcolor: '#F5A623',
                    color: 'black',
                    p: 1,
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                      REFUND
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      100% Refundable if you change your mind
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      before your group of {appConfig.maxGroupSize} is formed!
                    </Typography>
                  </Box>
                </Box>

                <Button
                  variant="outlined"
                  fullWidth
                  color="error"
                  onClick={() => setWithdrawalDialogOpen(true)}
                  sx={{ borderRadius: 2, borderColor: 'rgba(255, 255, 255, 0.23)', color: 'white' }}
                >
                  Withdraw
                </Button>
              </Paper>
            </Box>
          </CardContent>
        </Card>

        {/* Bottom Navigation */}
        <Paper
          elevation={3}
          sx={bottomNavStyle}
        >
          <Container maxWidth="sm">
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-around',
              py: 1
            }}>
              <Button
                color="inherit"
                sx={navButtonStyle}
              >
                <NotificationsIcon />
                <Typography variant="caption">Notification</Typography>
              </Button>

              <Button
                color="primary"
                sx={homeButtonStyle}
              >
                <HomeIcon />
                <Typography variant="caption">Home</Typography>
              </Button>

              <Button
                color="inherit"
                sx={navButtonStyle}
              >
                <SettingsIcon />
                <Typography variant="caption">Settings</Typography>
              </Button>
            </Box>
          </Container>
        </Paper>
      </Container>

      {/* Withdrawal Confirmation Dialog */}
      <Dialog
        open={withdrawalDialogOpen}
        onClose={handleWithdrawalCancel}
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
          Confirm Withdrawal
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Are you sure you want to withdraw from your group? Your ${appConfig.membershipPrice} payment will be refunded.
          </Typography>
          <Alert severity="info" sx={{ mb: 2 }}>
            Your refund will be processed within 3-5 business days to your original payment method.
          </Alert>
          <Typography variant="body2" color="text.secondary">
            You're welcome to join another group at any time.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)', px: 3, py: 2 }}>
          <Button
            onClick={handleWithdrawalCancel}
            variant="contained"
            sx={secondaryButtonStyle}
          >
            Hold On
          </Button>
          <Button
            onClick={handleWithdrawalConfirm}
            variant="outlined"
            color="error"
            sx={{ borderColor: 'rgba(255, 255, 255, 0.23)', color: 'white' }}
          >
            Withdraw Anyway
          </Button>
        </DialogActions>
      </Dialog>

      {/* Show withdrawal success message if user has withdrawn */}
      {isWithdrawn && (
        <Alert
          severity="success"
          variant="filled"
          sx={{
            position: 'fixed',
            top: 16,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 9999,
            maxWidth: 500,
            width: '90%'
          }}
        >
          Your withdrawal has been processed. Refund will arrive in 3-5 business days.
        </Alert>
      )}
    </Box>
  );
};

export default PaymentSuccess;
