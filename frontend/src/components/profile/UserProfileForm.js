import React, { useState, useEffect, useContext } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import ImageGallery from '../common/ImageGallery';
import {
  Box,
  Button,
  Container,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Typography,
  Alert,
  Chip,
  Divider,
  Checkbox,
  useTheme,
  alpha,
  Tooltip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import GroupsIcon from '@mui/icons-material/Groups';
import { ThemeContext } from '../../contexts/ThemeContext';

const UserProfileForm = ({ user }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { mode } = useContext(ThemeContext);

  // Check for redirect message and withdrawal status
  const redirectMessage = location.state?.message;
  const isWithdrawn = location.state?.withdrawn === true;

  // If user has withdrawn, clear the location state to prevent going back
  useEffect(() => {
    if (isWithdrawn) {
      window.history.replaceState({}, document.title);
    }
  }, [isWithdrawn]);
  const [formData, setFormData] = useState({
    name: '',
    zipCode: '',
    gender: '',
    interests: [],
    bio: '',
    profession: '',
    ethnicity: '',
    employmentStatus: '',
    relationshipStatus: '',
    locationEnabled: true,
    availableTimeSlots: []
  });
  const [errors, setErrors] = useState({});
  const [serverError, setServerError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [isProfileComplete, setIsProfileComplete] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);

  // Time slots available for selection
  const timeSlots = [
    { id: 'THURSDAY_7PM', label: 'Thursday 7:00–9:00pm' },
    { id: 'FRIDAY_7PM', label: 'Friday 7:00–9:00pm' },
    { id: 'SATURDAY_BRUNCH', label: 'Saturday 11:00am–1:00pm' }
  ];

  // Interests options
  const interestOptions = [
    'Reading', 'Hiking', 'Cooking', 'Photography', 'Gaming',
    'Music', 'Travel', 'Sports', 'Art', 'Technology',
    'Movies', 'Dancing', 'Writing', 'Yoga', 'Chess'
  ];

  // Load user data if available
  useEffect(() => {
    if (user) {
      // Try to load profile data from localStorage first
      const storedUser = JSON.parse(localStorage.getItem('user') || '{}');

      if (storedUser.profile) {
        // Use stored profile data
        setFormData(storedUser.profile);
        setIsProfileComplete(true);
      } else {
        // Use empty form data if no stored profile exists
        setFormData({
          name: '',
          zipCode: '',
          gender: '',
          interests: [],
          bio: '',
          profession: '',
          ethnicity: '',
          employmentStatus: '',
          relationshipStatus: '',
          locationEnabled: true,
          availableTimeSlots: []
        });
      }
    }
  }, [user]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // For name field, only allow letters and spaces
    if (name === 'name') {
      // Replace any non-letter characters (except spaces)
      const sanitizedValue = value.replace(/[^A-Za-z\s]/g, '');
      setFormData({
        ...formData,
        [name]: sanitizedValue
      });
    }
    // For zipCode field, be more flexible but clean it up for display
    else if (name === 'zipCode') {
      // Allow the browser's auto-populated value, but clean it for display if needed
      // This keeps the original value but will be validated properly on submit
      setFormData({
        ...formData,
        [name]: value
      });
    }
    else {
      setFormData({
        ...formData,
        [name]: value
      });
    }

    // Clear field-specific error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  const handleTimeSlotChange = (e) => {
    const { value } = e.target;
    setFormData({
      ...formData,
      availableTimeSlots: value
    });
  };

  const handleInterestToggle = (interest) => {
    const currentInterests = [...formData.interests];
    const interestIndex = currentInterests.indexOf(interest);

    if (interestIndex === -1) {
      // Add interest if not already selected
      if (currentInterests.length < 5) {
        currentInterests.push(interest);
      }
    } else {
      // Remove interest if already selected
      currentInterests.splice(interestIndex, 1);
    }

    setFormData({
      ...formData,
      interests: currentInterests
    });
  };

  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (!/^[A-Za-z\s]+$/.test(formData.name)) {
      newErrors.name = 'Name can only contain letters and spaces';
    }

    // Zip code validation
    if (!formData.zipCode.trim()) {
      newErrors.zipCode = 'ZIP code is required';
    } else {
      // More flexible ZIP code validation that accepts browser auto-populated values
      // Remove any non-digit characters for validation
      const cleanZip = formData.zipCode.replace(/[^0-9]/g, '');

      // US ZIP codes are 5 digits or 5+4 digits
      if (cleanZip.length !== 5 && cleanZip.length !== 9) {
        newErrors.zipCode = 'ZIP code should be 5 digits';
      }
    }

    // Gender validation
    if (!formData.gender) {
      newErrors.gender = 'Gender is required';
    }

    // Time slots validation
    if (formData.availableTimeSlots.length === 0) {
      newErrors.availableTimeSlots = 'Please select at least one time slot';
    }

    // Bio validation - make it mandatory
    if (!formData.bio || formData.bio.trim().length < 10) {
      newErrors.bio = 'Please share something unique about yourself (at least 10 characters). This field is required.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Extra check for bio field
    if (!formData.bio || formData.bio.trim().length < 10) {
      setErrors({
        ...errors,
        bio: 'Please share something unique about yourself (at least 10 characters). This field is required.'
      });
      // Scroll to the bio field
      document.getElementById('bio').scrollIntoView({ behavior: 'smooth', block: 'center' });
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setServerError('');
    setSuccess('');

    try {
      // Save profile data to localStorage for persistence
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const updatedUser = {
        ...currentUser,
        profile: {
          ...formData,
          isComplete: true,
          lastUpdated: new Date().toISOString()
        }
      };
      localStorage.setItem('user', JSON.stringify(updatedUser));

      // Also update the user in localUsers array for consistency
      const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');
      const userIndex = localUsers.findIndex(u => u.userId === currentUser.userId);
      if (userIndex !== -1) {
        localUsers[userIndex] = {
          ...localUsers[userIndex],
          profile: {
            ...formData,
            isComplete: true,
            lastUpdated: new Date().toISOString()
          }
        };
        localStorage.setItem('localUsers', JSON.stringify(localUsers));
      }

      // In a real app, this would be an API call
      // For now, we'll simulate a successful profile update
      setTimeout(() => {
        setSuccess('Profile updated successfully!');
        setIsProfileComplete(true);
        setLoading(false);
        setSuccessDialogOpen(true); // Open the success dialog

        // After successful profile update, check if user should be redirected to group pages
        const localGroups = JSON.parse(localStorage.getItem('localGroups') || '[]');
        const userGroup = localGroups.find(group =>
          group.members && group.members.some(member => member.id === user.userId)
        );

        if (userGroup) {
          // If group has 4 members (complete), go to group summary
          if (userGroup.members.length === 4) {
            setTimeout(() => navigate('/group-summary'), 2000);
          } else {
            // Otherwise go to group status
            setTimeout(() => navigate('/group-status'), 2000);
          }
        } else {
          // User has profile but no group yet
          setTimeout(() => navigate('/group-preview'), 2000);
        }
      }, 800);

      // Example of how the actual API call would look:
      /*
      const response = await fetch('/api/users/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update profile');
      }

      setSuccess('Profile updated successfully!');
      setIsProfileComplete(true);
      */
    } catch (err) {
      setServerError(err.message || 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleFindGroup = () => {
    // First validate and save the form data
    if (!validateForm()) {
      alert('Please complete your profile before finding a group');
      return;
    }

    // Save profile data to localStorage
    const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
    const updatedUser = {
      ...currentUser,
      profile: formData
    };
    localStorage.setItem('user', JSON.stringify(updatedUser));

    setIsProfileComplete(true);
    setSuccess('Profile saved! Finding your tribe...');

    // Navigate to group page after a short delay
    setTimeout(() => {
      navigate('/group');
    }, 800);
  };

  return (
    <Container component="main" maxWidth="md">
      <Paper
        elevation={6}
        sx={{
          p: { xs: 3, md: 5 },
          mt: 4,
          borderRadius: 3,
          background: mode === 'dark'
            ? `linear-gradient(145deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.6)})`
            : `linear-gradient(145deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.6)})`,
          backdropFilter: 'blur(10px)',
          boxShadow: mode === 'dark'
            ? '0 8px 32px rgba(0, 0, 0, 0.3)'
            : '0 8px 32px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
        }}
      >
        <Typography
          component="h1"
          variant="h4"
          align="center"
          gutterBottom
          sx={{
            fontWeight: 700,
            mb: 3,
            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textAlign: 'center'
          }}
        >
          Your Profile
        </Typography>

        {redirectMessage && (
          <Alert
            severity="warning"
            sx={{
              mb: 3,
              borderRadius: 2,
              boxShadow: `0 4px 12px ${alpha(theme.palette.warning.main, 0.2)}`
            }}
          >
            {redirectMessage}
          </Alert>
        )}

        {serverError && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 2,
              boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.2)}`
            }}
          >
            {serverError}
          </Alert>
        )}

        {success && (
          <Alert
            severity="success"
            sx={{
              mb: 3,
              borderRadius: 2,
              boxShadow: `0 4px 12px ${alpha(theme.palette.success.main, 0.2)}`
            }}
          >
            {success}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  '&::after': {
                    content: '""',
                    display: 'block',
                    height: 2,
                    flexGrow: 1,
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    ml: 2,
                    borderRadius: 1
                  }
                }}
              >
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                id="name"
                label="Full Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={!!errors.name}
                helperText={errors.name}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                id="zipCode"
                label="ZIP Code"
                name="zipCode"
                value={formData.zipCode}
                onChange={handleChange}
                error={!!errors.zipCode}
                helperText={errors.zipCode}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl component="fieldset" required error={!!errors.gender}>
                <FormLabel component="legend">Gender</FormLabel>
                <RadioGroup
                  row
                  name="gender"
                  value={formData.gender}
                  onChange={handleChange}
                >
                  <FormControlLabel value="male" control={<Radio />} label="Male" />
                  <FormControlLabel value="female" control={<Radio />} label="Female" />
                  <FormControlLabel value="other" control={<Radio />} label="Other" />
                </RadioGroup>
                {errors.gender && (
                  <Typography variant="caption" color="error">
                    {errors.gender}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            {/* Availability */}
            <Grid item xs={12}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  mt: 4,
                  fontWeight: 600,
                  color: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  '&::after': {
                    content: '""',
                    display: 'block',
                    height: 2,
                    flexGrow: 1,
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    ml: 2,
                    borderRadius: 1
                  }
                }}
              >
                Availability
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <FormControl
                fullWidth
                required
                error={!!errors.availableTimeSlots}
                sx={{
                  mt: 2,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              >
                <InputLabel id="time-slots-label">Available Time Slots</InputLabel>
                <Select
                  labelId="time-slots-label"
                  id="availableTimeSlots"
                  name="availableTimeSlots"
                  multiple
                  value={formData.availableTimeSlots}
                  onChange={handleTimeSlotChange}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, py: 1 }}>
                      {selected.map((value) => {
                        const slot = timeSlots.find(slot => slot.id === value);
                        return (
                          <Chip
                            key={value}
                            label={slot ? slot.label : value}
                            size="small"
                            sx={{
                              borderRadius: 1,
                              '& .MuiChip-label': { px: 1 }
                            }}
                          />
                        );
                      })}
                    </Box>
                  )}
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: 300,
                        borderRadius: 8
                      },
                    },
                  }}
                >
                  {timeSlots.map((slot) => (
                    <MenuItem key={slot.id} value={slot.id}>
                      {slot.label}
                    </MenuItem>
                  ))}
                </Select>
                {errors.availableTimeSlots && (
                  <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                    {errors.availableTimeSlots}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            {/* Interests */}
            <Grid item xs={12}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  mt: 4,
                  fontWeight: 600,
                  color: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  '&::after': {
                    content: '""',
                    display: 'block',
                    height: 2,
                    flexGrow: 1,
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    ml: 2,
                    borderRadius: 1
                  }
                }}
              >
                Interests (Select up to 5)
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {interestOptions.map((interest) => (
                  <Chip
                    key={interest}
                    label={interest}
                    onClick={() => handleInterestToggle(interest)}
                    color={formData.interests.includes(interest) ? "primary" : "default"}
                    sx={{ m: 0.5 }}
                  />
                ))}
              </Box>
            </Grid>

            {/* Additional Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Additional Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                id="bio"
                label="Share something unique about yourself *"
                name="bio"
                multiline
                rows={4}
                value={formData.bio}
                onChange={handleChange}
                placeholder="Tell us something interesting or unique about yourself (minimum 10 characters)"
                required
                error={!!errors.bio}
                helperText={errors.bio || 'This field is required'}
                InputProps={{
                  sx: {
                    borderColor: errors.bio ? 'error.main' : 'primary.main',
                    '&.Mui-focused': {
                      borderColor: errors.bio ? 'error.main' : 'primary.main',
                    }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="profession"
                label="Profession"
                name="profession"
                value={formData.profession}
                onChange={handleChange}
                inputProps={{
                  style: {
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden'
                  }
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="ethnicity"
                label="Ethnicity"
                name="ethnicity"
                value={formData.ethnicity}
                onChange={handleChange}
                inputProps={{
                  style: {
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden'
                  }
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="employment-status-label">Employment Status</InputLabel>
                <Select
                  labelId="employment-status-label"
                  id="employmentStatus"
                  name="employmentStatus"
                  value={formData.employmentStatus}
                  onChange={handleChange}
                >
                  <MenuItem value="Full-time">Full-time</MenuItem>
                  <MenuItem value="Part-time">Part-time</MenuItem>
                  <MenuItem value="Self-employed">Self-employed</MenuItem>
                  <MenuItem value="Student">Student</MenuItem>
                  <MenuItem value="Unemployed">Unemployed</MenuItem>
                  <MenuItem value="Retired">Retired</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="relationship-status-label">Relationship Status</InputLabel>
                <Select
                  labelId="relationship-status-label"
                  id="relationshipStatus"
                  name="relationshipStatus"
                  value={formData.relationshipStatus}
                  onChange={handleChange}
                >
                  <MenuItem value="Single">Single</MenuItem>
                  <MenuItem value="In a relationship">In a relationship</MenuItem>
                  <MenuItem value="Married">Married</MenuItem>
                  <MenuItem value="Divorced">Divorced</MenuItem>
                  <MenuItem value="Widowed">Widowed</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.locationEnabled}
                    onChange={handleCheckboxChange}
                    name="locationEnabled"
                    color="primary"
                  />
                }
                label="Enable location sharing for better matching"
              />
            </Grid>

            {/* Submit Buttons */}
            <Grid item xs={12} sx={{ mt: 4, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                sx={{
                  minWidth: '160px',
                  py: 1.5,
                  borderRadius: 2,
                  fontWeight: 600,
                  boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
                  '&:hover': {
                    boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.4)}`,
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                {loading ? 'Saving...' : 'Save Profile'}
              </Button>

              <Button
                variant="contained"
                color="secondary"
                onClick={handleFindGroup}
                disabled={!isProfileComplete}
                startIcon={<GroupsIcon />}
                sx={{
                  minWidth: '160px',
                  py: 1.5,
                  borderRadius: 2,
                  fontWeight: 600,
                  boxShadow: `0 4px 12px ${alpha(theme.palette.secondary.main, 0.3)}`,
                  '&:hover': {
                    boxShadow: `0 6px 16px ${alpha(theme.palette.secondary.main, 0.4)}`,
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                {isProfileComplete ? 'Find My Tribe' : 'Complete Profile First'}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* Image Gallery */}
      <Box sx={{ mt: 6, mb: 4 }}>
        <ImageGallery variant="profile" />
      </Box>

      {/* Success Dialog */}
      <Dialog
        open={successDialogOpen}
        onClose={() => setSuccessDialogOpen(false)}
        aria-labelledby="profile-success-dialog-title"
        aria-describedby="profile-success-dialog-description"
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
            background: mode === 'dark'
              ? alpha(theme.palette.background.paper, 0.9)
              : alpha(theme.palette.background.paper, 0.9),
            backdropFilter: 'blur(10px)',
          }
        }}
      >
        <DialogTitle id="profile-success-dialog-title" sx={{
          pb: 1,
          fontWeight: 600,
          color: theme.palette.success.main
        }}>
          Profile Saved Successfully!
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="profile-success-dialog-description" sx={{ mb: 2 }}>
            Your profile has been updated successfully. You can now find your tribe!
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setSuccessDialogOpen(false)}
            variant="outlined"
            sx={{ mr: 1 }}
          >
            Continue Editing
          </Button>
          <Button
            onClick={() => navigate('/group')}
            variant="contained"
            color="primary"
            startIcon={<GroupsIcon />}
            autoFocus
          >
            Find My Tribe
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default UserProfileForm;
