#!/bin/bash

# Set the working directory to the frontend folder
cd "$(dirname "$0")"

# Create logs directory if it doesn't exist
mkdir -p logs

# Check if the auto-test-runner is already running
if pgrep -f "auto-test-runner.sh" > /dev/null; then
  echo "Auto test runner is already running."
  echo "To stop it, run: ./stop-automated-testing.sh"
  exit 1
fi

# Check if required files exist
if [ ! -f auto-test-runner.sh ]; then
  echo "ERROR: auto-test-runner.sh not found."
  exit 1
fi

if [ ! -f test-generator.js ]; then
  echo "ERROR: test-generator.js not found."
  exit 1
fi

if [ ! -f test-templates.js ]; then
  echo "ERROR: test-templates.js not found."
  exit 1
fi

# Make sure the script is executable
chmod +x auto-test-runner.sh

# Start the auto-test-runner in the background
./auto-test-runner.sh &

# Save the process ID
PID=$!

# Create a PID file
echo $PID > .test-runner.pid

# Print the process ID
echo "===================================================="
echo "Auto test runner started with PID $PID"
echo "You can check the progress in the logs directory"
echo "To stop the runner, run: ./stop-automated-testing.sh"
echo "===================================================="
