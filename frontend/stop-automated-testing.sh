#!/bin/bash

# Set the working directory to the frontend folder
cd "$(dirname "$0")"

# Check if PID file exists
if [ -f .test-runner.pid ]; then
  PID=$(cat .test-runner.pid)

  # Check if the process is still running
  if ps -p $PID > /dev/null; then
    echo "Stopping auto test runner with PID $PID..."
    kill $PID

    # Wait for the process to terminate
    sleep 2

    # Check if it's still running and force kill if necessary
    if ps -p $PID > /dev/null; then
      echo "Process still running. Sending SIGKILL..."
      kill -9 $PID
    fi

    echo "Auto test runner stopped."
  else
    echo "Process with PID $PID is not running."
  fi

  # Remove the PID file
  rm .test-runner.pid
else
  # Try to find the process by name
  PID=$(pgrep -f "auto-test-runner.sh")

  if [ -z "$PID" ]; then
    echo "Auto test runner is not running."
    exit 1
  fi

  echo "Stopping auto test runner with PID $PID..."
  kill $PID

  # Wait for the process to terminate
  sleep 2

  # Check if it's still running and force kill if necessary
  if ps -p $PID > /dev/null; then
    echo "Process still running. Sending SIGKILL..."
    kill -9 $PID
  fi

  echo "Auto test runner stopped."
fi

# Check for any orphaned node processes related to our tests
ORPHANED_PIDS=$(pgrep -f "node.*test-generator.js")

if [ ! -z "$ORPHANED_PIDS" ]; then
  echo "Cleaning up orphaned test generator processes..."
  kill $ORPHANED_PIDS 2>/dev/null
fi

echo "All test processes have been stopped."
