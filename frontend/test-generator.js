const fs = require('fs');
const path = require('path');
const templates = require('./test-templates');

// Configuration
const testDir = path.join(__dirname, 'src', '__tests__');
const componentsDir = path.join(__dirname, 'src', 'components');
const testProgressFile = path.join(__dirname, 'test-progress.json');

// Function to find all component files
function findComponentFiles() {
  const components = [];

  function scanDir(dir, relativePath = '') {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        scanDir(filePath, path.join(relativePath, file));
      } else if (file.endsWith('.js') && !file.endsWith('.test.js')) {
        // Skip index files and utility files
        if (file === 'index.js' || file.includes('utils') || file.includes('context')) {
          continue;
        }

        const componentName = path.basename(file, '.js');
        const importPath = `../components/${relativePath ? relativePath + '/' : ''}${componentName}`;

        components.push({
          name: componentName,
          path: filePath,
          importPath,
          testPath: path.join(testDir, `${componentName}.test.js`)
        });
      }
    }
  }

  scanDir(componentsDir);
  return components;
}

// Function to load or initialize progress
function loadProgress() {
  try {
    if (fs.existsSync(testProgressFile)) {
      return JSON.parse(fs.readFileSync(testProgressFile, 'utf8'));
    }
  } catch (error) {
    console.error('Error loading progress file:', error);
  }

  return {
    completedTests: [],
    currentTest: null,
    remainingTests: []
  };
}

// Function to save progress
function saveProgress(progress) {
  fs.writeFileSync(testProgressFile, JSON.stringify(progress, null, 2));
}

// Function to determine the component type
function determineComponentType(component) {
  try {
    const content = fs.readFileSync(component.path, 'utf8');

    // Check if it's an auth component
    if (
      (component.path.includes('/auth/') ||
       component.name.toLowerCase().includes('login') ||
       component.name.toLowerCase().includes('signup') ||
       component.name.toLowerCase().includes('register')) &&
      (content.includes('localStorage') || content.includes('token'))
    ) {
      return 'auth';
    }

    // Check if it's a form component
    if (
      content.includes('<form') ||
      content.includes('onSubmit') ||
      content.includes('handleSubmit') ||
      content.includes('TextField') ||
      content.includes('input')
    ) {
      return 'form';
    }

    // Default to basic component
    return 'basic';
  } catch (error) {
    console.error(`Error reading component file ${component.path}:`, error);
    return 'basic';
  }
}

// Function to create a new test file
function createTestFile(component) {
  // Determine the component type
  const componentType = determineComponentType(component);

  // Select the appropriate template
  let testContent;
  switch (componentType) {
    case 'auth':
      testContent = templates.authComponentTest(component.name, component.importPath);
      break;
    case 'form':
      testContent = templates.formComponentTest(component.name, component.importPath);
      break;
    default:
      testContent = templates.basicComponentTest(component.name, component.importPath);
  }

  // Create the test directory if it doesn't exist
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  // Write the test file
  fs.writeFileSync(component.testPath, testContent);
  console.log(`Created test file for ${component.name} (${componentType}) at ${component.testPath}`);
}

// Main function
function main() {
  try {
    // Find all components
    const components = findComponentFiles();

    if (components.length === 0) {
      console.log('No components found. Please check the components directory path.');
      return;
    }

    console.log(`Found ${components.length} components to test.`);

    // Load progress
    const progress = loadProgress();

    // Initialize progress if empty
    if (!progress.remainingTests.length && !progress.currentTest) {
      progress.remainingTests = components
        .filter(comp => !progress.completedTests.includes(comp.name))
        .map(comp => comp.name);

      console.log(`Initialized ${progress.remainingTests.length} components for testing.`);
    }

    // If we have a current test, check if it exists
    if (progress.currentTest) {
      const testPath = path.join(testDir, `${progress.currentTest}.test.js`);
      if (!fs.existsSync(testPath)) {
        // Current test doesn't exist, create it
        const component = components.find(comp => comp.name === progress.currentTest);
        if (component) {
          createTestFile(component);
        } else {
          console.error(`Component ${progress.currentTest} not found`);
          // Move to the next test
          progress.currentTest = progress.remainingTests.shift();
          console.log(`Moving to next component: ${progress.currentTest}`);
        }
      } else {
        console.log(`Test for ${progress.currentTest} already exists.`);
      }
    } else if (progress.remainingTests.length > 0) {
      // Start a new test
      progress.currentTest = progress.remainingTests.shift();
      console.log(`Starting test for component: ${progress.currentTest}`);

      const component = components.find(comp => comp.name === progress.currentTest);
      if (component) {
        createTestFile(component);
      } else {
        console.error(`Component ${progress.currentTest} not found`);
      }
    } else {
      console.log('All tests have been created!');
      return;
    }

    // Save progress
    saveProgress(progress);

    console.log(`Progress: ${progress.completedTests.length} completed, ` +
                `1 current (${progress.currentTest}), ` +
                `${progress.remainingTests.length} remaining.`);
  } catch (error) {
    console.error('Error in test generator:', error);
  }
}

// Run the main function
main();
