{"completedTests": [], "currentTest": "ApiTestDashboard", "remainingTests": ["Dashboard", "MatchingTab", "AvailabilitySelection", "<PERSON><PERSON>", "Signup", "UserRegistration", "VerifyMobile", "AnimatedBackground", "ColorPicker", "CustomThemeSelector", "Footer", "HomePage", "ImageGallery", "<PERSON><PERSON><PERSON>", "ProtectedRoute", "RedirectBasedOnProfile", "ThemeSwitcher", "GamesIceBreakers", "GroupFormationComplete", "GroupPreview", "GroupStatus", "GroupStatusNew", "GroupSummary", "PaymentLanding", "PaymentSuccess", "UserProfileForm", "FunFact", "QuizIntro", "QuizQuestion", "QuizResults"]}