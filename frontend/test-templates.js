// Test templates for different component types

// Basic component test template
exports.basicComponentTest = (componentName, importPath) => `
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import ${componentName} from '${importPath}';

// Mock the useNavigate hook and Link component
const mockedNavigate = jest.fn();
jest.mock('react-router-dom', () => {
  const originalModule = jest.requireActual('react-router-dom');
  return {
    ...originalModule,
    useNavigate: () => mockedNavigate,
    Link: ({ children, to, ...rest }) => {
      return (
        <a 
          href={to} 
          onClick={(e) => {
            e.preventDefault();
            mockedNavigate(to);
          }}
          {...rest}
        >
          {children}
        </a>
      );
    },
  };
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(() => JSON.stringify({
    userId: '123',
    name: 'Test User',
    email: '<EMAIL>'
  })),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock fetch API
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  })
);

describe('${componentName} Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    global.fetch.mockClear();
    mockedNavigate.mockClear();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  // Test Case 1: Basic Rendering
  test('renders ${componentName.toLowerCase()} component', () => {
    render(
      <BrowserRouter>
        <${componentName} />
      </BrowserRouter>
    );
    
    // Basic rendering test
    expect(document.body).toBeInTheDocument();
  });
});
`;

// Form component test template
exports.formComponentTest = (componentName, importPath) => `
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ${componentName} from '${importPath}';

// Mock the useNavigate hook and Link component
const mockedNavigate = jest.fn();
jest.mock('react-router-dom', () => {
  const originalModule = jest.requireActual('react-router-dom');
  return {
    ...originalModule,
    useNavigate: () => mockedNavigate,
    Link: ({ children, to, ...rest }) => {
      return (
        <a 
          href={to} 
          onClick={(e) => {
            e.preventDefault();
            mockedNavigate(to);
          }}
          {...rest}
        >
          {children}
        </a>
      );
    },
  };
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(() => JSON.stringify({
    userId: '123',
    name: 'Test User',
    email: '<EMAIL>'
  })),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock fetch API
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  })
);

describe('${componentName} Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    global.fetch.mockClear();
    mockedNavigate.mockClear();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  // Test Case 1: Basic Rendering
  test('renders ${componentName.toLowerCase()} component', () => {
    render(
      <BrowserRouter>
        <${componentName} />
      </BrowserRouter>
    );
    
    // Basic rendering test
    expect(document.body).toBeInTheDocument();
  });

  // Test Case 2: Form Submission
  test('handles form submission', async () => {
    // Mock successful fetch response
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ success: true }),
    });

    render(
      <BrowserRouter>
        <${componentName} />
      </BrowserRouter>
    );
    
    // Find and click the submit button
    const submitButton = screen.getByRole('button', { name: /submit|save|continue|next/i });
    fireEvent.click(submitButton);
    
    // Check if fetch was called
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled();
    });
  });
});
`;

// Auth component test template
exports.authComponentTest = (componentName, importPath) => `
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ${componentName} from '${importPath}';

// Mock the useNavigate hook and Link component
const mockedNavigate = jest.fn();
jest.mock('react-router-dom', () => {
  const originalModule = jest.requireActual('react-router-dom');
  return {
    ...originalModule,
    useNavigate: () => mockedNavigate,
    Link: ({ children, to, ...rest }) => {
      return (
        <a 
          href={to} 
          onClick={(e) => {
            e.preventDefault();
            mockedNavigate(to);
          }}
          {...rest}
        >
          {children}
        </a>
      );
    },
  };
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock fetch API
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ token: 'fake-token', userId: '123' }),
  })
);

describe('${componentName} Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    global.fetch.mockClear();
    mockedNavigate.mockClear();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  // Test Case 1: Basic Rendering
  test('renders ${componentName.toLowerCase()} component', () => {
    render(
      <BrowserRouter>
        <${componentName} />
      </BrowserRouter>
    );
    
    // Basic rendering test
    expect(document.body).toBeInTheDocument();
  });

  // Test Case 2: Successful Authentication
  test('handles successful authentication', async () => {
    // Mock successful fetch response
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ token: 'fake-token', userId: '123' }),
    });

    render(
      <BrowserRouter>
        <${componentName} />
      </BrowserRouter>
    );
    
    // Find and click the submit button
    const submitButton = screen.getByRole('button', { name: /login|sign up|submit/i });
    fireEvent.click(submitButton);
    
    // Check if localStorage was updated and navigation occurred
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'fake-token');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('userId', '123');
      expect(mockedNavigate).toHaveBeenCalled();
    });
  });

  // Test Case 3: Failed Authentication
  test('handles failed authentication', async () => {
    // Mock failed fetch response
    global.fetch.mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({ message: 'Invalid credentials' }),
    });

    render(
      <BrowserRouter>
        <${componentName} />
      </BrowserRouter>
    );
    
    // Find and click the submit button
    const submitButton = screen.getByRole('button', { name: /login|sign up|submit/i });
    fireEvent.click(submitButton);
    
    // Check if error message is displayed
    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
    });
  });
});
`;
