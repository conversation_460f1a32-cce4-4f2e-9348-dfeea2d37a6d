#!/bin/bash

# This script sets up the PostgreSQL database for the MyTribe application

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL is not installed. Please install PostgreSQL first."
    exit 1
fi

# Database configuration
DB_NAME="mytribe"
DB_USER="postgres"
DB_PASSWORD="postgres"

# Create database
echo "Creating database $DB_NAME..."
psql -U $DB_USER -c "CREATE DATABASE $DB_NAME;" || {
    echo "Failed to create database. It might already exist."
}

# Enable the earthdistance module for location-based queries
echo "Enabling earthdistance module..."
psql -U $DB_USER -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS cube;" || {
    echo "Failed to create cube extension."
}
psql -U $DB_USER -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS earthdistance;" || {
    echo "Failed to create earthdistance extension."
}

echo "Database setup complete!"
echo "You can now run the application with: mvn spring-boot:run"
