package com.mytribe.matcher;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;

import java.util.EnumSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.Test;

import com.mytribe.filter.UserFilter;
import com.mytribe.model.Group;
import com.mytribe.model.TimeSlot;
import com.mytribe.model.User;
import com.mytribe.util.DistanceCalculator;

// This test suite follows the TDD-driven matching logic using Given / Expected / Reason format
public class GroupMatcherTest {

	// Scenario 1: First user joins an empty system
	// Given: One user joins, no groups or users exist
	// Expected result: User is unmatched and stored in waiting pool
	// Reason: No one to match with — store for future matching
	@Test
	public void test_first_user_is_unmatched_when_no_groups_or_users_exist() {
		GroupMatcher matcher = new GroupMatcher();
		User user = dummyUser("male", 32.96015, -96.83808);
		Optional<Group> matches = matcher.findMatchingGroups(user);
		assertTrue(matches.isEmpty());
	}

	// Scenario 2: No groups found within radius
	// Given: User joins, but no group found in 10-mile radius
	// Expected result: User is stored in waiting pool
	// Reason: Spatial boundary not satisfied — fallback to pending queue
	@Test
	public void test_user_is_unmatched_when_no_group_found_in_radius() {
		GroupMatcher matcher = new GroupMatcher();
		matcher.addGroup(dummyGroup("male", 33.5, -97.5, 1, TimeSlot.THURSDAY_7PM)); // ~60 miles
		User user = dummyUser("male", 32.96015, -96.83808);
		Optional<Group> matches = matcher.findMatchingGroups(user);
		assertTrue(matches.isEmpty());
	}

	// Scenario 3A: One valid group found — match succeeds
	// Given: One group exists, same gender, not full, within 10 miles
	// Expected result: User is matched to that group
	// Reason: All filters satisfied
	@Test
	public void test_user_matches_single_valid_group_within_radius() {
		GroupMatcher matcher = new GroupMatcher();
		Group group = dummyGroup("male", 32.96100, -96.83850, 2, TimeSlot.THURSDAY_7PM);
		matcher.addGroup(group);
		User user = dummyUser("male", 32.96015, -96.83808, TimeSlot.THURSDAY_7PM);
		Optional<Group> matches = matcher.findMatchingGroups(user);
		assertTrue(matches.isPresent());
		assertEquals(group.id, matches.get().id);
	}

	// Scenario 3B: One group found, gender mismatch
	// Given: Group exists, but gender differs
	// Expected result: No match
	// Reason: Gender mismatch violates hard filter
	@Test
	public void test_user_does_not_match_group_with_gender_mismatch() {
		GroupMatcher matcher = new GroupMatcher();
		matcher.addGroup(dummyGroup("female", 32.96100, -96.83850, 1, TimeSlot.THURSDAY_7PM));
		User user = dummyUser("male", 32.96015, -96.83808);
		Optional<Group> matches = matcher.findMatchingGroups(user);
		assertTrue(matches.isEmpty());
	}

	// Scenario 3C: One group found, but it's full
	// Given: Group exists, same gender, but size is 4
	// Expected result: No match
	// Reason: Group is finalized and cannot accept more members
	@Test
	public void test_user_does_not_match_full_group() {
		GroupMatcher matcher = new GroupMatcher();
		matcher.addGroup(dummyGroup("male", 32.96100, -96.83850, 4, TimeSlot.THURSDAY_7PM));
		User user = dummyUser("male", 32.96015, -96.83808);
		Optional<Group> matches = matcher.findMatchingGroups(user);
		assertTrue(matches.isEmpty());
	}

	// Scenario 4A: Two groups found — both match
	// Given: Two valid groups, same gender, within 10 miles
	// Expected result: Both returned; user can choose
	// Reason: Multiple valid options exist
	@Test
	public void test_user_matches_multiple_valid_groups() {
		GroupMatcher matcher = new GroupMatcher();
		Group g1 = dummyGroup("male", 32.96100, -96.83850, 1, TimeSlot.THURSDAY_7PM);
		Group g2 = dummyGroup("male", 32.96050, -96.83810, 2, TimeSlot.THURSDAY_7PM);
		matcher.addGroup(g1);
		matcher.addGroup(g2);
		User user = dummyUser("male", 32.96015, -96.83808);
		Optional<Group> matches = matcher.findMatchingGroups(user);
		assertEquals(2, matches.get().members.size());
		assertTrue(matches.get().equals(g1) || matches.get().equals(g2));
	}

	// Scenario 4B: Two groups found, one invalid
	// Given: One full, one valid
	// Expected result: Only valid group is returned
	// Reason: Filters should exclude non-eligible groups
	@Test
	public void test_user_only_matches_eligible_group_from_multiple() {
		GroupMatcher matcher = new GroupMatcher();
		Group valid = dummyGroup("male", 32.96100, -96.83850, 2, TimeSlot.THURSDAY_7PM);
		Group full = dummyGroup("male", 32.96000, -96.83800, 4, TimeSlot.THURSDAY_7PM);
		matcher.addGroup(valid);
		matcher.addGroup(full);
		User user = dummyUser("male", 32.96015, -96.83808);
		Optional<Group> matches = matcher.findMatchingGroups(user);
		assertTrue(matches.isPresent());
		assertEquals(valid.id, matches.get().id);
	}

	// Scenario 5: No matching groups, try to form new group with another user
	// Given: No group matched, but another waiting user is nearby
	// Expected result: New group is formed
	// Reason: If two users meet conditions, initiate new group
	@Test
	public void test_user_forms_new_group_with_nearby_unmatched_user() {
		GroupMatcher matcher = new GroupMatcher();
		User user1 = dummyUser("male", 32.96015, -96.83808);
		User user2 = dummyUser("male", 32.96150, -96.83900);
		matcher.addToWaitingPool(user2);
		Optional<Group> matches = matcher.findMatchingGroups(user1);
		assertTrue(matches.isPresent());
		assertEquals(2, matches.get().members.size());
	}

	// Scenario 6: Add more users to temp group; finalize at 4
	// Given: Temp group of 2 already exists; 2 more matching users arrive
	// Expected result: Group grows and becomes finalized at 4
	// Reason: New users satisfy conditions and max out group
	@Test
	public void test_temp_group_grows_to_finalized_at_four_members() {
		GroupMatcher matcher = new GroupMatcher();

		User u2 = dummyUser("male", 32.96020, -96.83810);
		User u3 = dummyUser("male", 32.96030, -96.83820);
		User u4 = dummyUser("male", 32.96040, -96.83830);
		matcher.addToWaitingPool(u2);
		matcher.addToWaitingPool(u3);
		matcher.addToWaitingPool(u4);

		User u1 = dummyUser("male", 32.96010, -96.83800);
		Optional<Group> matches = matcher.findMatchingGroups(u1);
		assertTrue(matches.isPresent());
		assertTrue(matches.get().isFinalized);
		assertEquals(4, matches.get().members.size());
	}

	// Scenario 7: Nearby users found but fail filters (e.g. gender)
	// Given: User is near others, but conditions not met
	// Expected result: No group formed
	// Reason: Fails hard filters even though spatial proximity exists
	@Test
	public void test_group_not_formed_if_nearby_users_fail_filters() {
		GroupMatcher matcher = new GroupMatcher();
		User male = dummyUser("male", 32.96010, -96.83800);
		User female = dummyUser("female", 32.96015, -96.83810);
		matcher.addToWaitingPool(female);
		Optional<Group> matches = matcher.findMatchingGroups(male);
		assertTrue(matches.isEmpty());
	}

	// Scenario 8: Multiple group combinations possible — choose best fit
	// Given: Several nearby unmatched users qualify
	// Expected result: System selects best fit by proximity
	// Reason: Prefer tighter grouping (lowest spread)
	@Test
	public void test_best_fit_group_formed_from_multiple_unmatched_users() {
		GroupMatcher matcher = new GroupMatcher();
		matcher.addToWaitingPool(dummyUser("male", 32.96010, -96.83800));
		matcher.addToWaitingPool(dummyUser("male", 32.96190, -96.83890));
		matcher.addToWaitingPool(dummyUser("male", 33.10000, -97.00000)); // too far
		User newUser = dummyUser("male", 32.96015, -96.83808);
		Optional<Group> matches = matcher.findMatchingGroups(newUser);
		assertTrue(matches.isPresent());
		assertEquals(3, matches.get().members.size());
		assertEquals(32.96071666666666, matches.get().centerLat, 0.0001);
		assertEquals(-96.83832666666666, matches.get().centerLon, 0.0001);
	}

	// Scenario 9: Group exists, within 10 miles, but is already finalized
	// Given: Group has 4 users (full), user is nearby and same gender
	// Expected result: User is not matched to that group
	// Reason: Finalized groups are closed and must reject further additions
	@Test
	public void test_user_does_not_join_finalized_group_even_if_nearby() {
		GroupMatcher matcher = new GroupMatcher();
		Group fullGroup = dummyGroup("male", 32.96010, -96.83800, 4, TimeSlot.THURSDAY_7PM);
		matcher.addGroup(fullGroup);

		User newUser = dummyUser("male", 32.96012, -96.83801); // within 10 miles
		Optional<Group> matches = matcher.findMatchingGroups(newUser);

		assertTrue(matches.isEmpty()); // user not added
		assertEquals(4, fullGroup.members.size()); // group remains full
	}

	// Scenario 10: Forming group exists with 2 users, user arrives nearby
	// Given: One forming group already exists with 2 users of same gender, new user is nearby
	// Expected result: User is added to the same forming group, increasing size to 3
	// Reason: Forming groups can grow until 4 members as long as proximity and gender match
	@Test
	public void test_user_joins_existing_forming_group_with_nearby_members() {
		GroupMatcher matcher = new GroupMatcher();

		// Form initial temp group with u1 and u2
		User u1 = dummyUser("male", 32.96010, -96.83800);
		User u2 = dummyUser("male", 32.96015, -96.83808);
		matcher.addToWaitingPool(u1);
		Optional<Group> g1 = matcher.findMatchingGroups(u2); // Forms group with u1
		assertTrue(g1.isPresent());
		Group tempGroup = g1.get();
		assertEquals(2, tempGroup.members.size());

		// u3 arrives — same gender, nearby — should join same group
		User u3 = dummyUser("male", 32.96012, -96.83805);
		Optional<Group> g2 = matcher.findMatchingGroups(u3);

		assertTrue(g2.isPresent());
		assertSame(tempGroup, g2.get()); // should be the same group as g1
		assertEquals(3, g2.get().members.size());
		assertTrue(g2.get().members.contains(u1));
		assertTrue(g2.get().members.contains(u2));
		assertTrue(g2.get().members.contains(u3));
	}

	// Scenario 11: Mixed attempts to join a forming group — only matching user is accepted
	// Given: A forming group has 2 male users; a nearby female, a far male, and a nearby male try to join
	// Expected result: Only the nearby male is added; female and distant male are rejected
	// Reason: Only users matching gender and within 10-mile radius should be allowed to join
	@Test
	public void test_only_matching_user_is_added_to_forming_group() {
		GroupMatcher matcher = new GroupMatcher();

		// Forming group with 2 males
		User u1 = dummyUser("male", 32.96010, -96.83800);
		User u2 = dummyUser("male", 32.96020, -96.83810);
		matcher.addToWaitingPool(u1);
		matcher.findMatchingGroups(u2); // Forms a group

		// Nearby female — same location, wrong gender
		User female = dummyUser("female", 32.96015, -96.83808);
		Optional<Group> result1 = matcher.findMatchingGroups(female);
		assertTrue(result1.isEmpty());

		// Distant male — right gender, wrong location
		User distantMale = dummyUser("male", 34.00000, -97.00000);
		Optional<Group> result2 = matcher.findMatchingGroups(distantMale);
		assertTrue(result2.isEmpty());

		// Nearby matching male — right gender and distance
		User matchingMale = dummyUser("male", 32.96018, -96.83805);
		Optional<Group> result3 = matcher.findMatchingGroups(matchingMale);
		assertTrue(result3.isPresent());
		assertEquals(3, result3.get().members.size());
		assertTrue(result3.get().members.contains(matchingMale));
	}

	// Scenario 12: Complex user stream — forming and finalized groups validation
	// Given: A mixed stream of 18 users with alternating matching and non-matching users
	// Expected result: Only correct users are grouped; unmatched remain out
	// Reason: Ensures precision across group formation, filters, and finalization
	@Test
	public void test_complex_user_stream_with_full_validation() {
		GroupMatcher matcher = new GroupMatcher();

		User u1 = dummyUser("male", 32.96010, -96.83800);
		Optional<Group> g1 = matcher.findMatchingGroups(u1);
		assertTrue(g1.isEmpty());

		User u2 = dummyUser("female", 32.96012, -96.83805);
		Optional<Group> g2 = matcher.findMatchingGroups(u2);
		assertTrue(g2.isEmpty());

		User u3 = dummyUser("male", 32.96011, -96.83802); // matches u1
		Optional<Group> g3 = matcher.findMatchingGroups(u3);
		assertTrue(g3.isPresent());
		Group maleGroup1 = g3.get();
		assertEquals(2, maleGroup1.members.size());

		User u4 = dummyUser("male", 33.10000, -97.10000); // too far
		Optional<Group> g4 = matcher.findMatchingGroups(u4);
		assertTrue(g4.isEmpty());

		User u5 = dummyUser("female", 34.00000, -98.00000); // too far
		Optional<Group> g5 = matcher.findMatchingGroups(u5);
		assertTrue(g5.isEmpty());

		User u6 = dummyUser("female", 32.96015, -96.83807); // matches u2
		Optional<Group> g6 = matcher.findMatchingGroups(u6);
		assertTrue(g6.isPresent());
		Group femaleGroup1 = g6.get();
		assertEquals(2, femaleGroup1.members.size());

		User u7 = dummyUser("female", 35.00000, -100.00000); // too far
		Optional<Group> g7 = matcher.findMatchingGroups(u7);
		assertTrue(g7.isEmpty());

		User u8 = dummyUser("female", 32.96016, -96.83806); // matches female group
		Optional<Group> g8 = matcher.findMatchingGroups(u8);
		assertTrue(g8.isPresent());
		assertEquals(3, g8.get().members.size());

		User u9 = dummyUser("female", 36.00000, -101.00000); // far again
		Optional<Group> g9 = matcher.findMatchingGroups(u9);
		assertTrue(g9.isEmpty());

		User u10 = dummyUser("male", 32.96017, -96.83809); // matches male group
		Optional<Group> g10 = matcher.findMatchingGroups(u10);
		assertTrue(g10.isPresent());
		assertEquals(3, g10.get().members.size());

		User u11 = dummyUser("male", 32.96018, -96.83810); // final member in male group
		Optional<Group> g11 = matcher.findMatchingGroups(u11);
		assertTrue(g11.isPresent());
		Group finalMaleGroup = g11.get();
		assertTrue(finalMaleGroup.isFinalized);
		assertEquals(4, finalMaleGroup.members.size());

		User u12 = dummyUser("male", 32.96050, -96.83850); // starts new male group
		Optional<Group> g12 = matcher.findMatchingGroups(u12);
		assertTrue(g12.isEmpty());

		User u13 = dummyUser("male", 35.00000, -96.00000); // too far
		Optional<Group> g13 = matcher.findMatchingGroups(u13);
		assertTrue(g13.isEmpty());

		User u14 = dummyUser("male", 32.96051, -96.83851); // matches u12
		Optional<Group> g14 = matcher.findMatchingGroups(u14);
		assertTrue(g14.isPresent());
		Group maleGroup2 = g14.get();
		assertEquals(2, maleGroup2.members.size());

		User u15 = dummyUser("male", 32.96052, -96.83852);
		Optional<Group> g15 = matcher.findMatchingGroups(u15);
		assertTrue(g15.isPresent());
		assertEquals(3, g15.get().members.size());

		User u16 = dummyUser("male", 32.96053, -96.83853);
		Optional<Group> g16 = matcher.findMatchingGroups(u16);
		assertTrue(g16.isPresent());
		Group finalizedMaleGroup2 = g16.get();
		assertTrue(finalizedMaleGroup2.isFinalized);
		assertEquals(4, finalizedMaleGroup2.members.size());

		User u17 = dummyUser("male", 34.00000, -96.00000); // far
		Optional<Group> g17 = matcher.findMatchingGroups(u17);
		assertTrue(g17.isEmpty());

		User u18 = dummyUser("male", 32.96055, -96.83855); // no open group, new wait
		Optional<Group> g18 = matcher.findMatchingGroups(u18);
		assertTrue(g18.isEmpty());
	}

	// Scenario 12: Complex user stream — forming and finalized groups validation
	// Given: A mixed stream of 18 users with alternating matching and non-matching users
	// Expected result: Only correct users are grouped; unmatched remain out
	// Reason: Ensures precision across group formation, filters, and finalization
	@Test
	public void test_complex_user_stream_with_full_validation_2() {
		GroupMatcher matcher = new GroupMatcher();

		List<User> users = List.of(
				dummyUser("male", 32.96010, -96.83800),
				dummyUser("female", 32.96012, -96.83805),
				dummyUser("male", 32.96011, -96.83802),
				dummyUser("male", 33.10000, -97.10000),
				dummyUser("female", 34.00000, -98.00000),
				dummyUser("female", 32.96015, -96.83807),
				dummyUser("female", 35.00000, -100.00000),
				dummyUser("female", 32.96016, -96.83806),
				dummyUser("female", 36.00000, -101.00000),
				dummyUser("male", 32.96017, -96.83809),
				dummyUser("male", 32.96018, -96.83810),
				dummyUser("male", 32.96050, -96.83850),
				dummyUser("male", 35.00000, -96.00000),
				dummyUser("male", 32.96051, -96.83851),
				dummyUser("male", 32.96052, -96.83852),
				dummyUser("male", 32.96053, -96.83853),
				dummyUser("male", 34.00000, -96.00000),
				dummyUser("male", 32.96055, -96.83855)
		);

		for (User user : users) {
			Optional<Group> matches = matcher.findMatchingGroups(user);
			System.out.println("---");
			System.out.println("User: " + user.name + " (" + user.gender + ") @ " + user.lat + ", " + user.lon);
			System.out.println("Waiting Pool Size: " + matcher.getUnmatchedUsers().size());

			if (!matches.isPresent()) {
				System.out.println("No group formed.");
			} else {
				Group group = matches.get();
					System.out.println("Group ID: " + group.id + ", Finalized: " + group.isFinalized);
					System.out.println("Members:");
					for (User member : group.members) {
						System.out.println("- " + member.name + " @ " + member.lat + ", " + member.lon);
					}
					System.out.println("Inter-member distances:");
					for (int i = 0; i < group.members.size(); i++) {
						for (int j = i + 1; j < group.members.size(); j++) {
							double dist = DistanceCalculator.calculateMiles(
									group.members.get(i).lat, group.members.get(i).lon,
									group.members.get(j).lat, group.members.get(j).lon);
							System.out.println(group.members.get(i).name + " <-> " + group.members.get(j).name + ": " + String.format("%.4f mi", dist));
						}
					}
			}

			System.out.println("================ Waiting Pool ================");
			for (User w : matcher.getUnmatchedUsers()) {
				System.out.println("- " + w.name + " (" + w.gender + ") @ " + w.lat + ", " + w.lon);
			}
		}

		System.out.println("================ Final Group Summary ================");
		int totalGroups = matcher.getInProgressGroups().size();
		System.out.println("Total groups formed: " + totalGroups);
		for (Group group : matcher.getInProgressGroups()) {
			System.out.println("Group ID: " + group.id + " | Finalized: " + group.isFinalized);
			System.out.println("Group Center: " + group.centerLat + ", " + group.centerLon);
			for (User member : group.members) {
				double distToCenter = DistanceCalculator.calculateMiles(
						group.centerLat, group.centerLon, member.lat, member.lon);
				System.out.println("- " + member.name + " @ " + member.lat + ", " + member.lon + " | Distance to center: " + String.format("%.4f mi", distToCenter));
			}
			System.out.println();
		}
	}

	// Scenario 13: User is exactly 10 miles from another user
	// Given: One user is in waiting pool, another joins exactly 10 miles away
	// Expected result: They should form a group
	// Reason: Distance threshold is inclusive (<= 10.0), so boundary users qualify
	@Test
	public void test_user_joins_when_distance_is_exactly_ten_miles() {
		GroupMatcher matcher = new GroupMatcher();

		User u1 = dummyUser("male", 32.96010, -96.83800);
		matcher.addToWaitingPool(u1);

		User u2 = dummyUser("male", 33.10460, -96.83800);
		Optional<Group> result = matcher.findMatchingGroups(u2);

		assertTrue(result.isPresent());
		assertEquals(2, result.get().members.size());
		assertTrue(result.get().members.contains(u1));
		assertTrue(result.get().members.contains(u2));
	}

	// Scenario 14: User has an unknown or unsupported gender
	// Given: A user with gender set to 'other' joins
	// Expected result: User is not matched or added to any group
	// Reason: Matching is strictly based on binary gender filtering in current rules
	@Test
	public void test_user_with_unknown_gender_is_not_matched() {
		GroupMatcher matcher = new GroupMatcher();

		User u1 = dummyUser("other", 32.96010, -96.83800);
		Optional<Group> result = matcher.findMatchingGroups(u1);

		assertTrue(result.isEmpty());
		assertTrue(matcher.getUnmatchedUsers().contains(u1)); // Remains unmatched
	}

	// Scenario 15: Same user is processed more than once
	// Given: One user is matched and then processed again
	// Expected result: User is only matched once; not re-added or duplicated
	// Reason: A user should only belong to one group and not be matched repeatedly
	@Test
	public void test_same_user_not_matched_multiple_times() {
		GroupMatcher matcher = new GroupMatcher();

		User u1 = dummyUser("male", 32.96010, -96.83800);
		User u2 = dummyUser("male", 32.96012, -96.83802);

		// u1 enters first
		matcher.addToWaitingPool(u1);

		// u2 matches with u1 — group is created
		Optional<Group> firstMatch = matcher.findMatchingGroups(u2);
		assertTrue(firstMatch.isPresent());
		Group formedGroup = firstMatch.get();
		assertEquals(2, formedGroup.members.size());

		// Now try matching u1 again
		Optional<Group> reMatch = matcher.findMatchingGroups(u1);
		assertFalse(reMatch.isEmpty()); // existing group is returned
		assertTrue(reMatch.get().members.contains(u2));
		assertTrue(reMatch.get().members.contains(u1));
	}

	// Scenario 16: Two users of different genders enter at exactly the same location
	// Given: A male and a female user both join at the same coordinates
	// Expected result: Two separate groups are formed
	// Reason: Gender is a hard filter — users must group by same gender
	@Test
	public void test_parallel_groups_formed_for_different_genders() {
		GroupMatcher matcher = new GroupMatcher();

		User male = dummyUser("male", 32.96010, -96.83800);
		matcher.addToWaitingPool(male);

		User female = dummyUser("female", 32.96010, -96.83800);
		Optional<Group> fResult = matcher.findMatchingGroups(female);
		assertFalse(fResult.isPresent());

		User female2 = dummyUser("female", 32.96010, -96.83800);
		Optional<Group> fGroup = matcher.findMatchingGroups(female2);
		assertTrue(fGroup.isPresent());
		assertEquals("female", fGroup.get().gender);
		assertEquals(2, fGroup.get().members.size());

		User male2 = dummyUser("male", 32.96010, -96.83800);
		Optional<Group> mGroup = matcher.findMatchingGroups(male2);
		assertTrue(mGroup.isPresent());
		assertEquals("male", mGroup.get().gender);
		assertEquals(2, mGroup.get().members.size());

		System.out.println("=== Formed Groups ===");
		for (Group group : matcher.getInProgressGroups()) {
			System.out.println("Group ID: " + group.id + " | Gender: " + group.gender);
			for (User u : group.members) {
				System.out.println("- " + u.name + " (" + u.gender + ") @ " + u.lat + ", " + u.lon);
			}
			System.out.println();
		}
	}

	// Scenario 18: No user can join a group after it is finalized
	// Given: A group has 4 users and is marked finalized
	// Expected result: A 5th user cannot join this group
	// Reason: Finalized groups are locked and should reject additional members
	@Test
	public void test_fifth_user_cannot_join_finalized_group() {
		GroupMatcher matcher = new GroupMatcher();

		double lat = 32.96010;
		double lon = -96.83800;

		User u1 = dummyUser("male", lat, lon);
		User u2 = dummyUser("male", lat, lon);
		User u3 = dummyUser("male", lat, lon);
		User u4 = dummyUser("male", lat, lon);

		matcher.addToWaitingPool(u1);
		matcher.findMatchingGroups(u2);
		matcher.findMatchingGroups(u3);
		Group finalizedGroup = matcher.findMatchingGroups(u4).get();

		assertTrue(finalizedGroup.isFinalized);
		assertEquals(4, finalizedGroup.members.size());

		// Now a 5th user tries to join
		User u5 = dummyUser("male", lat, lon);
		Optional<Group> u5Matches = matcher.findMatchingGroups(u5);

		assertTrue(u5Matches.isEmpty());
		assertTrue(matcher.getUnmatchedUsers().contains(u5)); // Stays in waiting pool
	}

	// Scenario 19: Forming group must not absorb users meant for a finalized group
	// Given: One finalized group exists and a forming group starts nearby
	// Expected result: New users do not accidentally join finalized group; they start or join a valid forming group
	// Reason: Finalized groups are immutable and should not influence new group formation
	@Test
	public void test_users_do_not_join_finalized_group_and_form_their_own() {
		GroupMatcher matcher = new GroupMatcher();

		// Finalized group (4 users at same location)
		double lat = 32.96010;
		double lon = -96.83800;
		User u1 = dummyUser("male", lat, lon);
		User u2 = dummyUser("male", lat, lon);
		User u3 = dummyUser("male", lat, lon);
		User u4 = dummyUser("male", lat, lon);
		matcher.addToWaitingPool(u1);
		matcher.findMatchingGroups(u2);
		matcher.findMatchingGroups(u3);
		Group finalizedGroup = matcher.findMatchingGroups(u4).get();
		assertTrue(finalizedGroup.isFinalized);

		// Now add 2 new users at same location
		User u5 = dummyUser("male", lat, lon);
		User u6 = dummyUser("male", lat, lon);

		Optional<Group> g5 = matcher.findMatchingGroups(u5);
		Optional<Group> g6 = matcher.findMatchingGroups(u6);

		assertFalse(g5.isPresent()); // u5 starts a new group
		assertTrue(g6.isPresent()); // u6 joins u5
		Group newGroup = g6.get();

		assertFalse(newGroup.isFinalized);
		assertEquals(2, newGroup.members.size());
		assertTrue(newGroup.members.contains(u5));
		assertTrue(newGroup.members.contains(u6));
	}

	@Test
	public void test_complex_matching_with_timeslot_and_group_status_tracking() {
		GroupMatcher matcher = new GroupMatcher();
		UserFilter filter = new UserFilter();

		// Users setup (mix of close/far, gender, and 1+ timeslots)
		User u1 = dummyUser("male", 32.96010, -96.83800, TimeSlot.THURSDAY_7PM);
		User u2 = dummyUser("male", 32.96011, -96.83801, TimeSlot.THURSDAY_7PM, TimeSlot.FRIDAY_7PM);
		User u3 = dummyUser("male", 32.96012, -96.83802, TimeSlot.THURSDAY_7PM);

		User u4 = dummyUser("female", 32.96013, -96.83803, TimeSlot.FRIDAY_7PM);
		User u5 = dummyUser("female", 32.96014, -96.83804, TimeSlot.FRIDAY_7PM);
		User u6 = dummyUser("female", 32.96015, -96.83805, TimeSlot.FRIDAY_7PM, TimeSlot.SATURDAY_BRUNCH);
		User u7 = dummyUser("female", 32.96016, -96.83806, TimeSlot.FRIDAY_7PM);

		User u8 = dummyUser("male", 33.20000, -97.30000, TimeSlot.THURSDAY_7PM); // Far
		User u9 = dummyUser("male", 32.96020, -96.83820, TimeSlot.THURSDAY_7PM);

		User u10 = dummyUser("female", 34.00000, -98.00000, TimeSlot.FRIDAY_7PM); // Far
		User u11 = dummyUser("male", 32.96030, -96.83830, TimeSlot.SATURDAY_BRUNCH); // Partial match

		User u12 = dummyUser("female", 32.96040, -96.83840, TimeSlot.FRIDAY_7PM); // Add to existing female group
		User u13 = dummyUser("female", 32.96041, -96.83841, TimeSlot.FRIDAY_7PM); // Finalize female group

		User u14 = dummyUser("male", 32.96042, -96.83842, TimeSlot.THURSDAY_7PM); // Add to male group
		User u15 = dummyUser("male", 32.96043, -96.83843, TimeSlot.THURSDAY_7PM); // Finalize male group

		List<User> allUsers = List.of(
				u1, u2, u3, u4, u5, u6, u7, u8, u9, u10, u11, u12, u13, u14, u15
		);

		for (User u : allUsers) {
			Optional<Group> matchedGroups = matcher.findMatchingGroups(u);
			printSystemStatus(u, matchedGroups, matcher, filter);
		}

		List<Group> maleGroups = matcher.getExistingCompleteGroups().stream()
				.filter(g -> g.gender.equals("male"))
				.collect(Collectors.toList());
		assertEquals(1, maleGroups.size());
		assertTrue(maleGroups.get(0).isFinalized);
		assertEquals(4, maleGroups.get(0).members.size());
		assertTrue(maleGroups.get(0).members.containsAll(List.of(u1, u2, u3, u9)));

		List<Group> femaleGroups = matcher.getExistingCompleteGroups().stream()
				.filter(g -> g.gender.equals("female")).collect(Collectors.toList());
		assertEquals(1, femaleGroups.size());
		assertTrue(femaleGroups.get(0).isFinalized);
		assertEquals(4, femaleGroups.get(0).members.size());
		assertTrue(femaleGroups.get(0).members.containsAll(List.of(u4, u6, u7, u5)));

		// Validate shared time slots for each group
		for (Group g : matcher.getExistingCompleteGroups()) {
			for (User m : g.members) {
				List<TimeSlot> shared = filter.getSharedAvailabilityForGroupWithUser(g.members, m);
				assertFalse(shared.isEmpty());
			}
		}
	}

	@Test
	public void test_complex_matching_with_timeslot_and_group_status_tracking_all_men() {
		GroupMatcher matcher = new GroupMatcher();
		UserFilter filter = new UserFilter();

		// Users setup (all male, spread 5–15 miles apart, with overlapping availability)
		User u1 = dummyUser("male", 32.96010, -96.83800, TimeSlot.THURSDAY_7PM);
		User u2 = dummyUser("male", 33.00010, -96.85800, TimeSlot.THURSDAY_7PM); // ~5.1 mi
		User u3 = dummyUser("male", 33.03010, -96.87500, TimeSlot.THURSDAY_7PM); // ~10 mi
		User u4 = dummyUser("male", 33.04010, -96.88500, TimeSlot.THURSDAY_7PM); // ~11.2 mi
		User u5 = dummyUser("male", 33.05010, -96.89500, TimeSlot.THURSDAY_7PM); // ~12.3 mi
		User u6 = dummyUser("male", 33.06010, -96.90500, TimeSlot.THURSDAY_7PM); // ~13.4 mi
		User u7 = dummyUser("male", 33.07010, -96.91500, TimeSlot.THURSDAY_7PM); // ~14.5 mi

		List<User> allUsers = List.of(u1, u2, u3, u4, u5, u6, u7);

		for (User u : allUsers) {
			Optional<Group> matchedGroups = matcher.findMatchingGroups(u);
			printSystemStatus(u, matchedGroups, matcher, filter);
		}
	}

	@Test
	public void test_complex_matching_with_timeslot_and_group_status_tracking_all_different_timeslot() {
		GroupMatcher matcher = new GroupMatcher();
		UserFilter filter = new UserFilter();

		// Users setup (all male, spread 5–15 miles apart, with overlapping availability)
		User u1 = dummyUser("male", 32.96010, -96.83800, TimeSlot.THURSDAY_7PM);
		User u2 = dummyUser("male", 33.00010, -96.85800, TimeSlot.FRIDAY_7PM); // ~5.1 mi
		User u3 = dummyUser("male", 33.03010, -96.87500, TimeSlot.SATURDAY_BRUNCH); // ~10 mi
		User u4 = dummyUser("female", 33.04010, -96.88500, TimeSlot.FRIDAY_7PM); // ~11.2 mi
		User u5 = dummyUser("female", 33.05010, -96.89500, TimeSlot.THURSDAY_7PM); // ~12.3 mi
		User u6 = dummyUser("female", 33.06010, -96.90500, TimeSlot.SATURDAY_BRUNCH); // ~13.4 mi
		User u7 = dummyUser("female", 33.07010, -96.90500, TimeSlot.SATURDAY_BRUNCH); // ~13.4 mi

		List<User> allUsers = List.of(u1, u2, u3, u4, u5, u6, u7);

		for (User u : allUsers) {
			Optional<Group> matchedGroups = matcher.findMatchingGroups(u);
			printSystemStatus(u, matchedGroups, matcher, filter);
		}
	}

	@Test
	public void test_sparse_male_user_matching_with_8_to_10_mile_separation() {
		GroupMatcher matcher = new GroupMatcher();
		UserFilter filter = new UserFilter();

		// Users spaced ~8–10 miles apart from each other
		User u1 = dummyUser("male", 32.96010, -96.83800, TimeSlot.THURSDAY_7PM);  // Anchor
		User u2 = dummyUser("male", 33.04000, -96.84000, TimeSlot.THURSDAY_7PM);  // ~8.8 mi
		User u3 = dummyUser("male", 33.12000, -96.84200, TimeSlot.THURSDAY_7PM);  // ~17.8 mi
		User u4 = dummyUser("male", 33.20000, -96.84400, TimeSlot.THURSDAY_7PM);  // ~26.7 mi
		User u5 = dummyUser("male", 33.28000, -96.84600, TimeSlot.THURSDAY_7PM);  // ~35.6 mi
		User u6 = dummyUser("male", 33.36000, -96.84800, TimeSlot.THURSDAY_7PM);  // ~44.6 mi
		User u7 = dummyUser("male", 33.44000, -96.85000, TimeSlot.THURSDAY_7PM);  // ~53.6 mi

		List<User> allUsers = List.of(u1, u2, u3, u4, u5, u6, u7);

		for (User u : allUsers) {
			Optional<Group> matchedGroups = matcher.findMatchingGroups(u);
			printSystemStatus(u, matchedGroups, matcher, filter);
		}
	}

	private User dummyUser(String gender, double lat, double lon, TimeSlot... slots) {
		User user = new User("75001", lat, lon, "City", "TX", gender, "chess");
		user.availableTimeSlots = EnumSet.copyOf(List.of(slots));
		return user;
	}

	// --- Utility methods for tests ---
	private User dummyUser(String gender, double lat, double lon) {
		return new User("75001", lat, lon, "City", "TX", gender, "chess");
	}

	private Group dummyGroup(String gender, double lat, double lon, int size, TimeSlot timeSlot) {
		Group g = new Group(gender);
		for (int i = 0; i < size; i++) {
			g.addUser(dummyUser(gender, lat + i * 0.0001, lon + i * 0.0001, timeSlot));
		}
		return g;
	}

	@Test
	public void test_batch_matching_from_waiting_pool_of_10_users() {
		GroupMatcher matcher = new GroupMatcher();
		UserFilter filter = new UserFilter();

		// Create 10 users with same gender and time slot but varying locations within 10–15 miles
		List<User> users = List.of(
				dummyUser("male", 32.9601, -96.8380, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 32.9701, -96.8390, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 32.9801, -96.8400, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 32.9901, -96.8410, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 33.0001, -96.8420, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 33.0101, -96.8430, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 33.0201, -96.8440, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 33.0301, -96.8450, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 33.0401, -96.8460, TimeSlot.THURSDAY_7PM),
				dummyUser("male", 33.0501, -96.8470, TimeSlot.THURSDAY_7PM)
		);

		// Add all to waiting pool first (simulating users sitting idle)
		users.forEach(matcher::addToWaitingPool);

		// Now simulate dynamic matching run — one by one
		for (User u : users) {
			Optional<Group> matched = matcher.findMatchingGroups(u);
			printSystemStatus(u, matched, matcher, filter);
		}
	}

	@Test
	public void test_exit_user_removes_from_inprogress_group_and_updates_center() {
		GroupMatcher matcher = new GroupMatcher();

		User u1 = dummyUser("male", 32.96010, -96.83800, TimeSlot.THURSDAY_7PM);  // Anchor
		User u2 = dummyUser("male", 33.04000, -96.84000, TimeSlot.THURSDAY_7PM);  // ~8.8 mi
		User u3 = dummyUser("male", 33.12000, -96.84200, TimeSlot.THURSDAY_7PM);  // ~17.8 mi

		matcher.addToWaitingPool(u1);
		matcher.findMatchingGroups(u2); // forms group with u1
		Optional<Group> groupOpt = matcher.findMatchingGroups(u3); // joins group
		assertTrue(groupOpt.isPresent());

		Group group = groupOpt.get();
		assertEquals(3, group.members.size());
		assertTrue(group.members.containsAll(List.of(u1, u2, u3)));
		assertFalse(group.isFinalized);

		// Step 2: Remove u2 and verify
		matcher.exitUser(u2);

		assertEquals(2, group.members.size());
		assertFalse(group.members.contains(u2));

		double expectedLat = (u1.lat + u3.lat) / 2;
		double expectedLon = (u1.lon + u3.lon) / 2;

		assertEquals(expectedLat, group.centerLat, 0.0001);
		assertEquals(expectedLon, group.centerLon, 0.0001);
	}



	private void printSystemStatus(User currentUser, Optional<Group> matchedGroup, GroupMatcher matcher, UserFilter filter) {
		System.out.println("👤 User: " + currentUser.name + " (" + currentUser.gender + "), Available: " + currentUser.availableTimeSlots);

		if (matchedGroup.isEmpty()) {
			System.out.println("❌ This user " + currentUser.name + " was not matched to any group and has been added to the waiting pool.");
		} else {
			Group group = matchedGroup.get();
			if (group.members.size() == 1 && group.members.contains(currentUser)) {
				System.out.println("🆕 This user " + currentUser.name + " started a new group.");
			} else if (group.isFinalized && group.members.contains(currentUser)) {
				System.out.println("✅ This user " + currentUser.name + " finalized a group. Group now has " + group.members.size() + " members.");
			} else {
				System.out.println("🔗 This user " + currentUser.name + " joined an existing forming group.");
			}
		}

		System.out.println("📊 Current Group Status:");
		List<Group> activeGroups = matcher.getInProgressGroups();
		List<Group> completeGroups = matcher.getExistingCompleteGroups();

		if (activeGroups.isEmpty() && completeGroups.isEmpty()) {
			System.out.println("  ❕ No groups have been formed yet.");
		} else {
			List<Group> allGroups = Stream.concat(activeGroups.stream(), completeGroups.stream())
					.collect(Collectors.toList());
			for (Group g : allGroups) {
				System.out.println("  Group ID: " + g.id + " | Gender: " + g.gender + " | Finalized: " + g.isFinalized + " | Members: " + g.members.size());
				System.out.println("  - Members:");
				for (User member : g.members) {
					double distToGroupCenter = DistanceCalculator.calculateMiles(g.centerLat, g.centerLon, member.lat, member.lon);
					System.out.println("    • " + member.name + " @ " + member.lat + ", " + member.lon +
							" | Slots: " + member.availableTimeSlots +
							" | Distance from group center: " + String.format("%.2f", distToGroupCenter) + " mi");
				}
				if (g.members.contains(currentUser)) {
					List<TimeSlot> shared = filter.getSharedAvailabilityForGroupWithUser(g.members, currentUser);
					if (shared.isEmpty()) {
						System.out.println("  ⚠️ No common TimeSlots found between group and user " + currentUser.name + ".");
					} else {
						System.out.println("  ⏰ TimeSlots common to both group and user " + currentUser.name + ": " + shared);
					}
				}
			}
		}

		List<User> unmatched = matcher.getUnmatchedUsers();
		if (unmatched.isEmpty()) {
			System.out.println("🕗 Waiting Pool Users (0): None");
		} else {
			System.out.print("🕗 Waiting Pool Users (" + unmatched.size() + "): ");
			System.out.println(unmatched.stream().map(u -> u.name).collect(Collectors.toList()));
		}
		System.out.println("-----------------------------------------------------");
	}




}
