#!/bin/bash

# This script starts both the backend and frontend of the MyTribe application

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Check if required tools are installed
if ! command_exists java; then
    echo "Java is not installed. Please install Java 11 or higher."
    exit 1
fi

if ! command_exists mvn; then
    echo "Maven is not installed. Please install <PERSON><PERSON>."
    exit 1
fi

if ! command_exists node; then
    echo "Node.js is not installed. Please install Node.js."
    exit 1
fi

if ! command_exists npm; then
    echo "npm is not installed. Please install npm."
    exit 1
fi

# Check if PostgreSQL is running
if command_exists pg_isready; then
    pg_isready -q
    if [ $? -ne 0 ]; then
        echo "PostgreSQL is not running. Please start PostgreSQL."
        exit 1
    fi
else
    echo "Warning: Cannot check if PostgreSQL is running. Make sure it's running."
fi

# Start the backend in the background
echo "Starting the backend..."
mvn spring-boot:run &
BACKEND_PID=$!

# Wait for the backend to start
echo "Waiting for the backend to start..."
sleep 10

# Start the frontend
echo "Starting the frontend..."
echo ""
echo "For this development version, you can use these demo credentials:"
echo "Regular user: <EMAIL> / demo123"
echo "Admin user: <EMAIL> / demo123"
echo ""
echo "The application will be available at http://localhost:3000"
echo ""
cd frontend
npm start

# When the frontend is stopped, also stop the backend
kill $BACKEND_PID
