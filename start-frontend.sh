#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting MyTribe Frontend (Development Mode)${NC}"
echo -e "${YELLOW}Note: This script starts only the frontend in development mode.${NC}"
echo -e "${YELLOW}The backend server is not started, but demo accounts will work.${NC}"
echo

# Navigate to the frontend directory
cd frontend

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
  echo -e "${GREEN}Installing dependencies...${NC}"
  npm install
fi

# Start the development server
echo -e "${GREEN}Starting React frontend...${NC}"
echo
echo -e "${YELLOW}For this development version, you can use these demo credentials:${NC}"
echo -e "${YELLOW}Regular user: <EMAIL> / demo123${NC}"
echo -e "${YELLOW}Admin user: <EMAIL> / demo123${NC}"
echo
echo -e "${GREEN}The application will be available at http://localhost:3000${NC}"
echo

npm start
